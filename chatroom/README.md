# LiveWin 聊天室服务

基于 Go 语言和 WebSocket 开发的实时聊天室服务。

## 功能特性

- ⚡ WebSocket 实时通信
- 💬 实时弹幕消息
- 🎁 礼物消息广播
- 👥 在线用户管理
- 🏠 多房间支持
- 🚫 消息过滤

## 技术栈

- **框架**: Gin Web Framework
- **WebSocket**: Gorilla WebSocket
- **缓存**: Redis
- **消息格式**: JSON

## WebSocket 连接

### 连接地址
```
ws://localhost:8081/ws?room_id=1&user_id=1&username=test&nickname=测试用户&avatar=
```

### 连接参数
- `room_id`: 房间ID
- `user_id`: 用户ID
- `username`: 用户名
- `nickname`: 昵称
- `avatar`: 头像URL

## 消息格式

### 发送消息格式
```json
{
  "type": "chat",
  "content": "消息内容",
  "extra": {}
}
```

### 接收消息格式
```json
{
  "id": "消息ID",
  "type": "chat",
  "room_id": "房间ID",
  "user_id": "用户ID",
  "username": "用户名",
  "nickname": "昵称",
  "avatar": "头像",
  "level": 1,
  "content": "消息内容",
  "timestamp": "2023-01-01T00:00:00Z",
  "extra": {}
}
```

## 消息类型

### chat - 聊天消息
普通的文字聊天消息

```json
{
  "type": "chat",
  "content": "大家好！"
}
```

### gift - 礼物消息
礼物赠送消息，包含礼物信息

```json
{
  "type": "gift",
  "content": "感谢支持！",
  "extra": {
    "gift_id": "1",
    "gift_name": "玫瑰花",
    "gift_icon": "🌹",
    "gift_price": 5,
    "count": 1,
    "effect": "rose"
  }
}
```

### join - 用户加入
用户加入房间的系统消息

```json
{
  "type": "join",
  "content": "用户昵称 加入了直播间"
}
```

### leave - 用户离开
用户离开房间的系统消息

```json
{
  "type": "leave",
  "content": "用户昵称 离开了直播间"
}
```

### system - 系统消息
系统广播消息

```json
{
  "type": "system",
  "content": "系统消息内容"
}
```

## API 接口

### 获取房间信息
```
GET /api/v1/room/:id/info
```

### 获取在线人数
```
GET /api/v1/room/:id/online
```

## 房间管理

### Hub 结构
- 管理所有房间和客户端连接
- 处理消息广播
- 维护在线用户列表

### Room 结构
- 房间ID和标题
- 房间内的客户端列表
- 在线人数统计

### Client 结构
- 客户端连接信息
- 用户基本信息
- WebSocket 连接

## 消息过滤

- 消息长度限制（200字符）
- 消息类型验证
- 频率限制（防刷屏）

## 环境变量

```bash
CHATROOM_PORT=8081
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret-key
```

## 开发指南

### 本地开发

1. 安装依赖
```bash
go mod tidy
```

2. 启动服务
```bash
go run cmd/main.go
```

### 测试 WebSocket 连接

可以使用浏览器开发者工具测试：

```javascript
const ws = new WebSocket('ws://localhost:8081/ws?room_id=1&user_id=1&username=test&nickname=测试&avatar=');

ws.onopen = function() {
    console.log('连接成功');
    // 发送消息
    ws.send(JSON.stringify({
        type: 'chat',
        content: '你好，世界！'
    }));
};

ws.onmessage = function(event) {
    console.log('收到消息:', JSON.parse(event.data));
};
```

### Docker 部署

```bash
docker build -t livewin-chatroom .
docker run -p 8081:8081 livewin-chatroom
```
