package main

import (
	"log"
	"livewin/chatroom/internal/config"
	"livewin/chatroom/internal/hub"
	"livewin/chatroom/internal/router"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 创建聊天室Hub
	chatHub := hub.NewHub()
	go chatHub.Run()

	// 设置路由
	r := router.SetupRouter(chatHub)

	// 启动服务器
	log.Printf("Chatroom server starting on port %s", cfg.Port)
	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
