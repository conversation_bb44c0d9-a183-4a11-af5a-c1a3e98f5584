package models

import "time"

// MessageType 消息类型
type MessageType string

const (
	MessageTypeChat MessageType = "chat"
	MessageTypeGift MessageType = "gift"
	MessageTypeJoin MessageType = "join"
	MessageTypeLeave MessageType = "leave"
	MessageTypeSystem MessageType = "system"
)

// Message 聊天消息结构
type Message struct {
	ID        string      `json:"id"`
	Type      MessageType `json:"type"`
	RoomID    string      `json:"room_id"`
	UserID    string      `json:"user_id"`
	Username  string      `json:"username"`
	Nickname  string      `json:"nickname"`
	Avatar    string      `json:"avatar"`
	Level     int         `json:"level"`
	Content   string      `json:"content"`
	Timestamp time.Time   `json:"timestamp"`
	Extra     interface{} `json:"extra,omitempty"` // 额外数据，如礼物信息
}

// GiftMessage 礼物消息额外数据
type GiftMessage struct {
	GiftID    string `json:"gift_id"`
	GiftName  string `json:"gift_name"`
	GiftIcon  string `json:"gift_icon"`
	GiftPrice int64  `json:"gift_price"`
	Count     int    `json:"count"`
	Effect    string `json:"effect"`
}

// Client WebSocket客户端信息
type Client struct {
	ID       string `json:"id"`
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
	Level    int    `json:"level"`
	RoomID   string `json:"room_id"`
}

// Room 房间信息
type Room struct {
	ID          string             `json:"id"`
	Title       string             `json:"title"`
	OwnerID     string             `json:"owner_id"`
	Clients     map[string]*Client `json:"clients"`
	OnlineCount int                `json:"online_count"`
	CreatedAt   time.Time          `json:"created_at"`
}
