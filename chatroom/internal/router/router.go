package router

import (
	"livewin/chatroom/internal/handler"
	"livewin/chatroom/internal/hub"
	"livewin/chatroom/internal/middleware"
	"livewin/chatroom/internal/websocket"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

func SetupRouter(chatHub *hub.Hub) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())

	// 创建处理器
	roomHandler := handler.NewRoomHandler(chatHub)

	// WebSocket连接
	r.GET("/ws", func(c *gin.Context) {
		log.Printf("WebSocket connection request from %s at %s", c.ClientIP(), time.Now().Format("15:04:05"))
		websocket.HandleWebSocket(chatHub, c.Writer, c.Request)
	})

	// API路由组
	api := r.Group("/api/v1")
	{
		// 房间相关
		room := api.Group("/room")
		{
			room.GET("/:id/info", roomHandler.GetRoomInfo)
			room.GET("/:id/online", roomHandler.GetOnlineCount)
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok", "service": "chatroom"})
	})

	return r
}
