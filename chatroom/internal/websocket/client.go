package websocket

import (
	"encoding/json"
	"livewin/chatroom/internal/hub"
	"livewin/chatroom/internal/models"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

const (
	// 写入等待时间
	writeWait = 10 * time.Second

	// Pong等待时间
	pongWait = 60 * time.Second

	// Ping周期，必须小于pongWait
	pingPeriod = (pongWait * 9) / 10

	// 最大消息大小
	maxMessageSize = 512
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源，生产环境应该检查Origin
		return true
	},
}

// HandleWebSocket 处理WebSocket连接
func HandleWebSocket(chatHub *hub.Hub, w http.ResponseWriter, r *http.Request) {
	log.Printf("Attempting WebSocket upgrade for %s", r.RemoteAddr)

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket upgrade error: %v", err)
		return
	}

	log.Printf("WebSocket upgrade successful for %s", r.RemoteAddr)

	// 从查询参数获取用户信息
	userID := r.URL.Query().Get("user_id")
	username := r.URL.Query().Get("username")
	nickname := r.URL.Query().Get("nickname")
	avatar := r.URL.Query().Get("avatar")
	roomID := r.URL.Query().Get("room_id")

	log.Printf("WebSocket params - userID: %s, username: %s, roomID: %s", userID, username, roomID)

	if userID == "" || username == "" || roomID == "" {
		log.Printf("Missing required parameters, closing connection")
		conn.Close()
		return
	}

	// 创建客户端
	clientID := generateClientID()
	log.Printf("Creating client with ID: %s", clientID)

	client := &hub.Client{
		ID:       clientID,
		UserID:   userID,
		Username: username,
		Nickname: nickname,
		Avatar:   avatar,
		Level:    1, // 默认等级
		RoomID:   roomID,
		Conn:     conn,
		Send:     make(chan []byte, 256),
		Hub:      chatHub,
	}

	log.Printf("Registering client %s (%s) to room %s", clientID, username, roomID)
	// 注册客户端
	chatHub.RegisterClient(client)

	log.Printf("Starting goroutines for client %s", clientID)
	// 启动goroutines
	go writePump(client)
	go readPump(client)
}

// readPump 读取客户端消息
func readPump(c *hub.Client) {
	defer func() {
		c.Hub.UnregisterClient(c)
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(maxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(pongWait))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	for {
		_, messageBytes, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		log.Printf("Received raw message: %s", string(messageBytes))

		// 解析消息
		var incomingMessage struct {
			Type    models.MessageType `json:"type"`
			Content string             `json:"content"`
			Extra   interface{}        `json:"extra,omitempty"`
		}

		if err := json.Unmarshal(messageBytes, &incomingMessage); err != nil {
			log.Printf("Error unmarshaling message: %v", err)
			continue
		}

		log.Printf("Parsed message - Type: %s, Content: %s", incomingMessage.Type, incomingMessage.Content)

		// 创建消息对象
		message := &models.Message{
			ID:        generateMessageID(),
			Type:      incomingMessage.Type,
			RoomID:    c.RoomID,
			UserID:    c.UserID,
			Username:  c.Username,
			Nickname:  c.Nickname,
			Avatar:    c.Avatar,
			Level:     c.Level,
			Content:   incomingMessage.Content,
			Timestamp: time.Now(),
			Extra:     incomingMessage.Extra,
		}

		// 消息过滤和验证
		if !isValidMessage(message) {
			log.Printf("Message validation failed for message: %+v", message)
			continue
		}

		// 处理心跳消息
		if message.Type == "ping" {
			log.Printf("Received ping from client %s, sending pong", c.Username)
			pongMessage := map[string]string{"type": "pong"}
			if pongData, err := json.Marshal(pongMessage); err == nil {
				c.Conn.WriteMessage(websocket.TextMessage, pongData)
			}
			continue
		}

		log.Printf("Broadcasting message: %+v", message)
		// 广播消息
		c.Hub.BroadcastMessage(message)
	}
}

// writePump 向客户端写入消息
func writePump(c *hub.Client) {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				log.Printf("Client %s send channel closed", c.Username)
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			log.Printf("Writing message to client %s: %s", c.Username, string(message))
			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				log.Printf("Error getting writer for client %s: %v", c.Username, err)
				return
			}
			w.Write(message)

			// 添加排队的消息到当前消息
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				log.Printf("Error closing writer for client %s: %v", c.Username, err)
				return
			}
			log.Printf("Message successfully written to client %s", c.Username)

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// isValidMessage 验证消息是否有效
func isValidMessage(message *models.Message) bool {
	// 检查消息类型
	switch message.Type {
	case models.MessageTypeChat, models.MessageTypeGift:
		// 检查消息长度
		if len(message.Content) > 200 {
			return false
		}
		return true
	case "ping":
		// 心跳消息，直接返回true但不广播
		return true
	default:
		return false
	}
}

func generateClientID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(8)
}

func generateMessageID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(6)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
