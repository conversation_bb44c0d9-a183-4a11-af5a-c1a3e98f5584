package hub

import (
	"encoding/json"
	"livewin/chatroom/internal/models"
	"log"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// Hub 聊天室中心，管理所有房间和客户端
type Hub struct {
	// 房间映射 roomID -> Room
	rooms map[string]*Room

	// 注册客户端
	register chan *Client

	// 注销客户端
	unregister chan *Client

	// 广播消息
	broadcast chan *models.Message

	// 互斥锁
	mutex sync.RWMutex
}

// Room 房间结构
type Room struct {
	ID      string             `json:"id"`
	Title   string             `json:"title"`
	Clients map[string]*Client `json:"-"`
	mutex   sync.RWMutex
}

// Client WebSocket客户端
type Client struct {
	ID       string          `json:"id"`
	UserID   string          `json:"user_id"`
	Username string          `json:"username"`
	Nickname string          `json:"nickname"`
	Avatar   string          `json:"avatar"`
	Level    int             `json:"level"`
	RoomID   string          `json:"room_id"`
	Conn     *websocket.Conn `json:"-"`
	Send     chan []byte     `json:"-"`
	Hub      *Hub            `json:"-"`
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		rooms:      make(map[string]*Room),
		register:   make(chan *Client, 100),         // 添加缓冲避免阻塞
		unregister: make(chan *Client, 100),         // 添加缓冲避免阻塞
		broadcast:  make(chan *models.Message, 100), // 添加缓冲避免阻塞
	}
}

// Run 运行Hub
func (h *Hub) Run() {
	log.Printf("Hub started and running")
	for {
		select {
		case client := <-h.register:
			log.Printf("Hub received register request for client %s", client.ID)
			h.registerClient(client)
			log.Printf("Hub completed register for client %s", client.ID)

		case client := <-h.unregister:
			log.Printf("Hub received unregister request for client %s", client.ID)
			h.unregisterClient(client)
			log.Printf("Hub completed unregister for client %s", client.ID)

		case message := <-h.broadcast:
			log.Printf("Hub received broadcast message: %+v", message)
			h.broadcastMessage(message)
			log.Printf("Hub completed broadcast for message %s", message.ID)

		default:
			// 非阻塞处理，优先处理broadcast消息
			select {
			case message := <-h.broadcast:
				log.Printf("Hub received broadcast message (priority): %+v", message)
				h.broadcastMessage(message)
				log.Printf("Hub completed broadcast for message %s (priority)", message.ID)
			default:
				// 短暂休眠避免CPU占用过高
				time.Sleep(1 * time.Millisecond)
			}
		}
	}
}

// RegisterClient 注册客户端
func (h *Hub) RegisterClient(client *Client) {
	h.register <- client
}

// UnregisterClient 注销客户端
func (h *Hub) UnregisterClient(client *Client) {
	h.unregister <- client
}

// BroadcastMessage 广播消息
func (h *Hub) BroadcastMessage(message *models.Message) {
	log.Printf("BroadcastMessage called with message: %+v", message)
	if h == nil {
		log.Printf("ERROR: Hub is nil!")
		return
	}
	if h.broadcast == nil {
		log.Printf("ERROR: broadcast channel is nil!")
		return
	}
	log.Printf("Sending message to broadcast channel...")
	h.broadcast <- message
	log.Printf("Message sent to broadcast channel successfully")
}

func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 获取或创建房间
	room, exists := h.rooms[client.RoomID]
	if !exists {
		room = &Room{
			ID:      client.RoomID,
			Clients: make(map[string]*Client),
		}
		h.rooms[client.RoomID] = room
	}

	// 添加客户端到房间
	room.mutex.Lock()
	room.Clients[client.ID] = client
	room.mutex.Unlock()

	log.Printf("Client %s joined room %s", client.Username, client.RoomID)

	// 异步发送加入消息，避免阻塞注册过程
	go func() {
		joinMessage := &models.Message{
			ID:        generateMessageID(),
			Type:      models.MessageTypeJoin,
			RoomID:    client.RoomID,
			UserID:    client.UserID,
			Username:  client.Username,
			Nickname:  client.Nickname,
			Avatar:    client.Avatar,
			Level:     client.Level,
			Content:   client.Nickname + " 加入了直播间",
			Timestamp: time.Now(),
		}
		h.broadcastToRoom(client.RoomID, joinMessage)
	}()
}

func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	room, exists := h.rooms[client.RoomID]
	if !exists {
		return
	}

	room.mutex.Lock()
	if _, exists := room.Clients[client.ID]; exists {
		delete(room.Clients, client.ID)
		close(client.Send)

		// 如果房间没有客户端了，删除房间
		if len(room.Clients) == 0 {
			delete(h.rooms, client.RoomID)
		}
	}
	room.mutex.Unlock()

	log.Printf("Client %s left room %s", client.Username, client.RoomID)

	// 发送离开消息
	leaveMessage := &models.Message{
		ID:        generateMessageID(),
		Type:      models.MessageTypeLeave,
		RoomID:    client.RoomID,
		UserID:    client.UserID,
		Username:  client.Username,
		Nickname:  client.Nickname,
		Avatar:    client.Avatar,
		Level:     client.Level,
		Content:   client.Nickname + " 离开了直播间",
		Timestamp: time.Now(),
	}

	h.broadcastToRoom(client.RoomID, leaveMessage)
}

func (h *Hub) broadcastMessage(message *models.Message) {
	h.broadcastToRoom(message.RoomID, message)
}

func (h *Hub) broadcastToRoom(roomID string, message *models.Message) {
	h.mutex.RLock()
	room, exists := h.rooms[roomID]
	h.mutex.RUnlock()

	if !exists {
		log.Printf("Room %s not found for broadcast", roomID)
		return
	}

	messageBytes, err := json.Marshal(message)
	if err != nil {
		log.Printf("Error marshaling message: %v", err)
		return
	}

	room.mutex.RLock()
	clientCount := len(room.Clients)
	log.Printf("Broadcasting to %d clients in room %s", clientCount, roomID)

	for clientID, client := range room.Clients {
		select {
		case client.Send <- messageBytes:
			log.Printf("Message sent to client %s (%s)", clientID, client.Username)
		default:
			log.Printf("Client %s (%s) send channel blocked, removing client", clientID, client.Username)
			close(client.Send)
			delete(room.Clients, client.ID)
		}
	}
	room.mutex.RUnlock()
}

// GetRoomInfo 获取房间信息
func (h *Hub) GetRoomInfo(roomID string) *models.Room {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	room, exists := h.rooms[roomID]
	if !exists {
		return nil
	}

	room.mutex.RLock()
	defer room.mutex.RUnlock()

	clients := make(map[string]*models.Client)
	for id, client := range room.Clients {
		clients[id] = &models.Client{
			ID:       client.ID,
			UserID:   client.UserID,
			Username: client.Username,
			Nickname: client.Nickname,
			Avatar:   client.Avatar,
			Level:    client.Level,
			RoomID:   client.RoomID,
		}
	}

	return &models.Room{
		ID:          room.ID,
		Title:       room.Title,
		Clients:     clients,
		OnlineCount: len(clients),
		CreatedAt:   time.Now(),
	}
}

func generateMessageID() string {
	return time.Now().Format("20060102150405") + "-" + randomString(6)
}

func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
