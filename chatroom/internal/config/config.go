package config

import (
	"os"

	"github.com/joho/godotenv"
)

type Config struct {
	Port      string
	RedisURL  string
	JWTSecret string
}

func Load() (*Config, error) {
	// 加载.env文件
	if err := godotenv.Load(); err != nil {
		// 在生产环境中，.env文件可能不存在，这是正常的
	}

	config := &Config{
		Port:      getEnv("CHATROOM_PORT", "8082"),
		RedisURL:  getEnv("REDIS_URL", "redis://localhost:6379"),
		JWTSecret: getEnv("JWT_SECRET", "development"),
	}

	return config, nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
