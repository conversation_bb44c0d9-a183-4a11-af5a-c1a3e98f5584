package handler

import (
	"livewin/chatroom/internal/hub"
	"net/http"

	"github.com/gin-gonic/gin"
)

type RoomHandler struct {
	hub *hub.Hub
}

func NewRoomHandler(hub *hub.Hub) *RoomHandler {
	return &RoomHandler{
		hub: hub,
	}
}

func (h *RoomHandler) GetRoomInfo(c *gin.Context) {
	roomID := c.Param("id")
	if roomID == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "房间ID不能为空"})
		return
	}

	roomInfo := h.hub.GetRoomInfo(roomID)
	if roomInfo == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "房间不存在"})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"room": roomInfo,
	})
}

func (h *RoomHandler) GetOnlineCount(c *gin.Context) {
	roomID := c.<PERSON>m("id")
	if roomID == "" {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "房间ID不能为空"})
		return
	}

	roomInfo := h.hub.GetRoomInfo(roomID)
	if roomInfo == nil {
		c.JSON(http.StatusOK, gin.H{"online_count": 0})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"online_count": roomInfo.OnlineCount,
	})
}
