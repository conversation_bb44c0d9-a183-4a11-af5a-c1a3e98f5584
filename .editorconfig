# EditorConfig 配置文件
# 统一不同编辑器的代码格式

root = true

# 所有文件的默认设置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# Go 文件
[*.go]
indent_style = tab
indent_size = 4

# JavaScript/TypeScript 文件
[*.{js,jsx,ts,tsx}]
indent_style = space
indent_size = 2

# JSON 文件
[*.json]
indent_style = space
indent_size = 2

# YAML 文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# Makefile
[Makefile]
indent_style = tab

# Docker 文件
[Dockerfile*]
indent_style = space
indent_size = 2

# Shell 脚本
[*.sh]
indent_style = space
indent_size = 2

# CSS/SCSS 文件
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# HTML 文件
[*.html]
indent_style = space
indent_size = 2

# XML 文件
[*.xml]
indent_style = space
indent_size = 2

# SQL 文件
[*.sql]
indent_style = space
indent_size = 2
