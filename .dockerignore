# Docker 构建时忽略的文件和目录

# Git 相关
.git
.gitignore
.gitattributes

# 文档
README.md
*.md
docs/
documentation/

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
tmp/
temp/

# 备份文件
*.bak
*.backup

# 测试文件
*_test.go
test/
tests/
__tests__/
*.test.js
*.spec.js
test-*.html

# 覆盖率报告
coverage/
.nyc_output/
coverage.out
coverage.html

# 前端开发依赖和构建产物
frontend/node_modules/
frontend/.next/
frontend/build/
frontend/dist/
frontend/out/
frontend/.cache/
frontend/.parcel-cache/
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Go 构建产物
backend/*.exe
backend/main
backend/app
backend/server
backend/vendor/
backend/tmp/

chatroom/*.exe
chatroom/main
chatroom/app
chatroom/server
chatroom/vendor/
chatroom/tmp/

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# Docker 相关
Dockerfile*
docker-compose*.yml
.dockerignore

# 脚本文件（如果不需要在容器中）
scripts/
*.sh

# 部署相关
deploy/
deployment/
k8s/

# 监控配置
monitoring/
grafana/
prometheus/

# 证书和密钥
*.pem
*.key
*.crt
*.p12
secrets/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
cache/
.cache/

# 性能分析文件
*.prof
*.pprof
