#!/bin/bash

# LiveWin 直播平台启动脚本

echo "🚀 启动 LiveWin 直播平台..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建.env文件（如果不存在）
if [ ! -f .env ]; then
    echo "📝 创建 .env 配置文件..."
    cp .env.example .env
    echo "✅ 请编辑 .env 文件配置您的环境变量"
fi

# 启动服务
echo "🐳 启动 Docker 容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."

# 检查后端服务
if curl -f http://localhost:8080/health > /dev/null 2>&1; then
    echo "✅ 后端服务启动成功 (http://localhost:8080)"
else
    echo "❌ 后端服务启动失败"
fi

# 检查聊天室服务
if curl -f http://localhost:8081/health > /dev/null 2>&1; then
    echo "✅ 聊天室服务启动成功 (http://localhost:8081)"
else
    echo "❌ 聊天室服务启动失败"
fi

# 检查前端服务
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ 前端服务启动成功 (http://localhost:3000)"
else
    echo "❌ 前端服务启动失败"
fi

echo ""
echo "🎉 LiveWin 直播平台启动完成！"
echo "📱 前端地址: http://localhost:3000"
echo "🔧 后端API: http://localhost:8080"
echo "💬 聊天室: http://localhost:8081"
echo ""
echo "📋 常用命令:"
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
