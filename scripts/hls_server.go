package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"
)

func main() {
	// 创建HLS目录
	hlsDir := "./hls_content"
	if err := os.MkdirAll(hlsDir+"/wida/wida", 0755); err != nil {
		log.Fatal("Failed to create HLS directory:", err)
	}

	// 创建虚拟TS文件
	createTestFiles(hlsDir)

	// 设置路由
	http.HandleFunc("/wida/wida.m3u8", handlePlaylist)
	http.HandleFunc("/wida/wida/", handleSegment)
	http.HandleFunc("/health", handleHealth)

	port := "3001"
	fmt.Printf("🎥 HLS服务器启动在端口 %s\n", port)
	fmt.Printf("📺 测试流: http://localhost:%s/wida/wida.m3u8\n", port)
	fmt.Printf("🔍 健康检查: http://localhost:%s/health\n", port)

	log.Fatal(http.ListenAndServe(":"+port, nil))
}

func handlePlaylist(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/vnd.apple.mpegurl")
	w.Header().Set("Cache-Control", "no-cache")

	if r.Method == "OPTIONS" {
		return
	}

	// 生成动态播放列表
	seq := time.Now().Unix() % 1000
	playlist := fmt.Sprintf(`#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-MEDIA-SEQUENCE:%d
#EXT-X-PLAYLIST-TYPE:LIVE
#EXTINF:10.0,
/wida/wida/segment-%d.ts
#EXTINF:10.0,
/wida/wida/segment-%d.ts
#EXTINF:10.0,
/wida/wida/segment-%d.ts
#EXTINF:10.0,
/wida/wida/segment-%d.ts
#EXTINF:10.0,
/wida/wida/segment-%d.ts
`, seq, seq, seq+1, seq+2, seq+3, seq+4)

	w.Write([]byte(playlist))
	log.Printf("Served playlist with sequence %d", seq)
}

func handleSegment(w http.ResponseWriter, r *http.Request) {
	// 设置CORS头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "video/mp2t")

	if r.Method == "OPTIONS" {
		return
	}

	// 返回虚拟的TS内容
	tsContent := make([]byte, 1024*10) // 10KB的虚拟TS数据
	for i := range tsContent {
		tsContent[i] = 0x47 // TS包同步字节
	}

	w.Write(tsContent)
	log.Printf("Served TS segment: %s", r.URL.Path)
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	fmt.Fprintf(w, `{"status":"ok","service":"hls-server","time":"%s"}`, time.Now().Format(time.RFC3339))
}

func createTestFiles(hlsDir string) {
	// 创建一些虚拟的TS文件
	segmentDir := filepath.Join(hlsDir, "wida", "wida")
	for i := 0; i < 100; i++ {
		segmentPath := filepath.Join(segmentDir, "segment-"+strconv.Itoa(i)+".ts")
		content := make([]byte, 1024*10) // 10KB
		for j := range content {
			content[j] = 0x47
		}
		if err := os.WriteFile(segmentPath, content, 0644); err != nil {
			log.Printf("Failed to create segment %s: %v", segmentPath, err)
		}
	}
	fmt.Println("✅ Created test TS segments")
}
