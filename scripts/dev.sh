#!/bin/bash

# LiveWin 开发环境启动脚本

echo "🛠️  启动 LiveWin 开发环境..."

# 检查依赖
echo "📋 检查开发环境依赖..."

# 检查Go
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.19+"
    exit 1
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

# 检查并启动数据库服务
echo "💾 启动数据库服务..."

# 检查PostgreSQL
if command -v psql &> /dev/null; then
    echo "� 启动 PostgreSQL 服务..."
    sudo systemctl start postgresql 2>/dev/null || echo "PostgreSQL 已在运行"

    # 检查数据库是否存在，如果不存在则创建
    if ! sudo -u postgres psql -lqt | cut -d \| -f 1 | grep -qw livewin; then
        echo "🔧 创建数据库和用户..."
        sudo -u postgres createuser -s livewin 2>/dev/null || echo "用户已存在"
        sudo -u postgres createdb livewin -O livewin 2>/dev/null || echo "数据库已存在"
        sudo -u postgres psql -c "ALTER USER livewin PASSWORD 'password';" 2>/dev/null
    fi
else
    echo "❌ PostgreSQL 未安装，请先安装: sudo apt install postgresql postgresql-contrib"
    exit 1
fi

# 检查Redis
if command -v redis-cli &> /dev/null; then
    echo "🔴 启动 Redis 服务..."
    sudo systemctl start redis-server 2>/dev/null || echo "Redis 已在运行"
else
    echo "❌ Redis 未安装，请先安装: sudo apt install redis-server"
    exit 1
fi

sleep 2

# 创建.env文件
if [ ! -f .env ]; then
    echo "📝 创建开发环境配置..."
    cp .env.example .env
fi

# 启动后端服务
echo "🔧 启动后端服务..."
cd backend
go mod tidy
go run cmd/main.go &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动聊天室服务
echo "💬 启动聊天室服务..."
cd chatroom
go mod tidy
go run cmd/main.go &
CHATROOM_PID=$!
cd ..

# 等待聊天室启动
sleep 3

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
if [ ! -d node_modules ]; then
    echo "📦 安装前端依赖..."
    npm install
fi
npm start &
FRONTEND_PID=$!
cd ..

# 保存进程ID
echo $BACKEND_PID > .backend.pid
echo $CHATROOM_PID > .chatroom.pid
echo $FRONTEND_PID > .frontend.pid

echo ""
echo "🎉 开发环境启动完成！"
echo ""
echo "📱 前端开发服务器: http://localhost:3000"
echo "🔧 后端API服务器: http://localhost:8080"
echo "💬 聊天室服务器: http://localhost:8082"
echo ""
echo "📋 开发命令:"
echo "  停止所有服务: ./scripts/stop-dev.sh"
echo "  查看后端日志: tail -f backend/logs/app.log"
echo "  运行测试: ./scripts/test.sh"
echo ""
echo "⚠️  按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 停止开发服务..."; kill $BACKEND_PID $CHATROOM_PID $FRONTEND_PID 2>/dev/null; rm -f .*.pid; exit 0' INT

wait
