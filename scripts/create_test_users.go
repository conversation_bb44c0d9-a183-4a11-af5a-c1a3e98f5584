package main

import (
	"fmt"
	"log"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type User struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Username string `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email    string `json:"email" gorm:"uniqueIndex;size:100;not null"`
	Password string `json:"-" gorm:"not null"`
}

func main() {
	fmt.Println("👥 创建/更新测试用户")
	fmt.Println("===================")

	// 连接数据库
	dsn := "root:wida@tcp(localhost:3306)/livewin?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 测试用户列表
	testUsers := []struct {
		username string
		password string
		email    string
	}{
		{"wida", "123456", "<EMAIL>"},
		{"demo_streamer", "password123", "<EMAIL>"},
		{"admin", "admin123", "<EMAIL>"},
	}

	for _, testUser := range testUsers {
		// 生成密码哈希
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(testUser.password), bcrypt.DefaultCost)
		if err != nil {
			log.Printf("为用户 %s 生成密码哈希失败: %v", testUser.username, err)
			continue
		}

		// 检查用户是否存在
		var existingUser User
		result := db.Where("username = ?", testUser.username).First(&existingUser)
		
		if result.Error == gorm.ErrRecordNotFound {
			// 用户不存在，创建新用户
			newUser := User{
				Username: testUser.username,
				Email:    testUser.email,
				Password: string(hashedPassword),
			}
			
			if err := db.Create(&newUser).Error; err != nil {
				log.Printf("创建用户 %s 失败: %v", testUser.username, err)
				continue
			}
			
			fmt.Printf("✅ 创建用户: %s (密码: %s)\n", testUser.username, testUser.password)
		} else {
			// 用户存在，更新密码
			if err := db.Model(&existingUser).Update("password", string(hashedPassword)).Error; err != nil {
				log.Printf("更新用户 %s 密码失败: %v", testUser.username, err)
				continue
			}
			
			fmt.Printf("✅ 更新用户: %s (密码: %s)\n", testUser.username, testUser.password)
		}

		// 验证密码
		var user User
		if err := db.Where("username = ?", testUser.username).First(&user).Error; err != nil {
			log.Printf("验证用户 %s 失败: %v", testUser.username, err)
			continue
		}

		err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(testUser.password))
		if err != nil {
			fmt.Printf("❌ 用户 %s 密码验证失败\n", testUser.username)
		} else {
			fmt.Printf("✅ 用户 %s 密码验证成功\n", testUser.username)
		}
	}

	fmt.Println("\n🎉 测试用户设置完成!")
	fmt.Println("可用的登录凭据:")
	for _, testUser := range testUsers {
		fmt.Printf("   用户名: %s, 密码: %s\n", testUser.username, testUser.password)
	}
}
