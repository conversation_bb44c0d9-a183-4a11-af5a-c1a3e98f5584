#!/bin/bash

# LiveWin 直播间创建演示脚本

echo "🎬 LiveWin 直播间创建演示"
echo "=========================="

# API 基础URL
API_BASE="http://localhost:8080/api/v1"

# 检查后端服务是否运行
echo "🔍 检查后端服务状态..."
if ! curl -f "$API_BASE/../health" > /dev/null 2>&1; then
    echo "❌ 后端服务未运行，请先启动后端服务"
    echo "   运行命令: cd backend && go run cmd/main.go"
    exit 1
fi
echo "✅ 后端服务运行正常"

# 1. 注册测试用户
echo ""
echo "👤 步骤1: 注册测试用户..."
REGISTER_RESPONSE=$(curl -s -X POST "$API_BASE/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "streamer_demo",
    "email": "<EMAIL>",
    "password": "password123"
  }')

echo "注册响应: $REGISTER_RESPONSE"

# 2. 用户登录获取token
echo ""
echo "🔐 步骤2: 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "streamer_demo",
    "password": "password123"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "❌ 登录失败，无法获取token"
    exit 1
fi

echo "✅ 登录成功，获取到token: ${TOKEN:0:20}..."

# 3. 创建直播间
echo ""
echo "🏠 步骤3: 创建直播间..."
CREATE_ROOM_RESPONSE=$(curl -s -X POST "$API_BASE/room/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "title": "我的第一个直播间",
    "description": "欢迎来到我的直播间！这里有精彩的内容等着你。"
  }')

echo "创建直播间响应: $CREATE_ROOM_RESPONSE"

# 提取房间ID
ROOM_ID=$(echo $CREATE_ROOM_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)

if [ -z "$ROOM_ID" ]; then
    echo "❌ 创建直播间失败"
    exit 1
fi

echo "✅ 直播间创建成功！房间ID: $ROOM_ID"

# 4. 获取直播间详情
echo ""
echo "📋 步骤4: 获取直播间详情..."
ROOM_DETAILS=$(curl -s -X GET "$API_BASE/room/$ROOM_ID")
echo "直播间详情: $ROOM_DETAILS"

# 5. 开始直播
echo ""
echo "🎥 步骤5: 开始直播..."
START_LIVE_RESPONSE=$(curl -s -X POST "$API_BASE/room/$ROOM_ID/start" \
  -H "Authorization: Bearer $TOKEN")

echo "开始直播响应: $START_LIVE_RESPONSE"

# 6. 获取直播间列表
echo ""
echo "📺 步骤6: 获取直播间列表..."
LIVE_ROOMS=$(curl -s -X GET "$API_BASE/room/list")
echo "直播间列表: $LIVE_ROOMS"

echo ""
echo "🎉 演示完成！"
echo "=========================="
echo "📊 总结:"
echo "- 用户名: streamer_demo"
echo "- 房间ID: $ROOM_ID"
echo "- 直播状态: 已开始"
echo ""
echo "🌐 你可以通过以下方式访问:"
echo "- 前端页面: http://localhost:3000"
echo "- API文档: http://localhost:8080/health"
echo ""
echo "💡 提示:"
echo "- 推流密钥已自动生成"
echo "- 可以通过前端界面管理直播间"
echo "- 支持实时聊天和礼物功能"
