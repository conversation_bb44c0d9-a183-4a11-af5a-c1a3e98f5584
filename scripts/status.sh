#!/bin/bash

# LiveWin 服务状态检查脚本

echo "🔍 LiveWin 服务状态检查"
echo "======================"

# 检查数据库服务
echo "1. 数据库服务状态"
echo "----------------"

# PostgreSQL
if systemctl is-active --quiet postgresql; then
    echo "✅ PostgreSQL 服务运行中"
    if psql -h localhost -U livewin -d livewin -c "SELECT 1;" >/dev/null 2>&1; then
        echo "✅ PostgreSQL 数据库连接正常"
    else
        echo "❌ PostgreSQL 数据库连接失败"
    fi
else
    echo "❌ PostgreSQL 服务未运行"
fi

# Redis
if systemctl is-active --quiet redis-server; then
    echo "✅ Redis 服务运行中"
    if redis-cli ping >/dev/null 2>&1; then
        echo "✅ Redis 连接正常"
    else
        echo "❌ Redis 连接失败"
    fi
else
    echo "❌ Redis 服务未运行"
fi

echo ""
echo "2. 应用服务状态"
echo "----------------"

# 后端API
if curl -f -s http://localhost:8080/health >/dev/null 2>&1; then
    echo "✅ 后端API服务 (端口 8080) 运行正常"
else
    echo "❌ 后端API服务 (端口 8080) 未运行"
fi

# 聊天室服务
if curl -f -s http://localhost:8081/health >/dev/null 2>&1; then
    echo "✅ 聊天室服务 (端口 8081) 运行正常"
else
    echo "❌ 聊天室服务 (端口 8081) 未运行"
fi

# 前端服务
if curl -f -s http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ 前端服务 (端口 3000) 运行正常"
else
    echo "❌ 前端服务 (端口 3000) 未运行"
fi

echo ""
echo "3. 端口占用情况"
echo "----------------"

for port in 3000 8080 8081 5432 6379; do
    if lsof -i :$port >/dev/null 2>&1; then
        process=$(lsof -i :$port | tail -1 | awk '{print $1}')
        echo "✅ 端口 $port 被 $process 占用"
    else
        echo "⚪ 端口 $port 空闲"
    fi
done

echo ""
echo "4. 系统资源"
echo "----------------"

# 内存使用
memory_usage=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
echo "💾 内存使用率: ${memory_usage}%"

# 磁盘使用
disk_usage=$(df -h / | tail -1 | awk '{print $5}')
echo "💿 磁盘使用率: $disk_usage"

# CPU负载
load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
echo "⚡ CPU负载: $load_avg"

echo ""
echo "📋 快速操作"
echo "============"
echo "启动开发环境: ./scripts/dev.sh"
echo "停止开发环境: ./scripts/stop-dev.sh"
echo "运行测试: ./scripts/quick-test.sh"
echo "查看日志: docker-compose logs -f"
