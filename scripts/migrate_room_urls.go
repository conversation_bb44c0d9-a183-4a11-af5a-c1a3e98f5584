package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// Room 直播间模型 - 更新后的版本
type Room struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	UserID      uint   `json:"user_id" gorm:"not null"`
	Title       string `json:"title" gorm:"not null"`
	Description string `json:"description"`
	Cover       string `json:"cover"`
	StreamKey   string `json:"stream_key" gorm:"uniqueIndex;size:150;not null"`
	StreamURL   string `json:"stream_url" gorm:"size:255"` // 新增：直播播放地址
	PushURL     string `json:"push_url" gorm:"size:255"`   // 新增：推流地址
	IsLive      bool   `json:"is_live" gorm:"default:false"`
	ViewCount   int64  `json:"view_count" gorm:"default:0"`
	Category    string `json:"category"`
	Tags        string `json:"tags"`
}

func main() {
	fmt.Println("🔄 开始数据库迁移：添加直播地址字段")
	fmt.Println("=====================================")

	// 连接数据库
	dsn := "root:wida@tcp(localhost:3306)/livewin?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 自动迁移表结构 - 这会添加新的字段
	err = db.AutoMigrate(&Room{})
	if err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	fmt.Println("✅ 数据库表结构迁移完成")

	// 为现有的房间生成直播地址
	var rooms []Room
	if err := db.Find(&rooms).Error; err != nil {
		log.Fatal("查询房间失败:", err)
	}

	fmt.Printf("📊 找到 %d 个房间，开始生成直播地址...\n", len(rooms))

	for _, room := range rooms {
		// 生成直播地址
		baseURL := "rtmp://localhost:1935/live"
		hlsBaseURL := "http://localhost:8080/hls"

		pushURL := fmt.Sprintf("%s/%s", baseURL, room.StreamKey)
		streamURL := fmt.Sprintf("%s/%s.m3u8", hlsBaseURL, room.StreamKey)

		// 更新数据库
		err := db.Model(&room).Updates(map[string]interface{}{
			"push_url":   pushURL,
			"stream_url": streamURL,
		})

		if err != nil {
			log.Printf("❌ 更新房间 %d 失败: %v", room.ID, err)
			continue
		}

		fmt.Printf("✅ 房间 %d: 推流地址和播放地址已生成\n", room.ID)
		fmt.Printf("   推流地址: %s\n", pushURL)
		fmt.Printf("   播放地址: %s\n", streamURL)
	}

	fmt.Println("\n🎉 数据库迁移完成！")
	fmt.Println("=====================================")
	fmt.Println("📋 迁移总结:")
	fmt.Printf("- 添加了 stream_url 字段（播放地址）\n")
	fmt.Printf("- 添加了 push_url 字段（推流地址）\n")
	fmt.Printf("- 为 %d 个房间生成了直播地址\n", len(rooms))
	fmt.Println("\n💡 提示:")
	fmt.Println("- 前端现在可以从后端获取直播地址")
	fmt.Println("- 播放地址用于前端视频播放器")
	fmt.Println("- 推流地址用于OBS等推流软件")
	fmt.Println("- 可以通过 /api/v1/room/{id}/stream 接口获取直播信息")
}
