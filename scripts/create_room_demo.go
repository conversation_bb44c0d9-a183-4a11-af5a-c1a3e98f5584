package main

import (
	"crypto/md5"
	"fmt"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type User struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Username string `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email    string `json:"email" gorm:"uniqueIndex;size:100;not null"`
	Password string `json:"-" gorm:"not null"`
	Nickname string `json:"nickname" gorm:"size:50"`
	Avatar   string `json:"avatar" gorm:"size:255"`
	Level    int    `json:"level" gorm:"default:1"`
	Coins    int64  `json:"coins" gorm:"default:1000"`
	IsStreamer bool `json:"is_streamer" gorm:"default:false"`
	Status   string `json:"status" gorm:"default:active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Room struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	User        *User     `json:"user"`
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description"`
	Cover       string    `json:"cover"`
	StreamKey   string    `json:"stream_key" gorm:"uniqueIndex;size:150;not null"`
	StreamURL   string    `json:"stream_url" gorm:"size:255"`
	PushURL     string    `json:"push_url" gorm:"size:255"`
	IsLive      bool      `json:"is_live" gorm:"default:false"`
	ViewCount   int64     `json:"view_count" gorm:"default:0"`
	Category    string    `json:"category"`
	Tags        string    `json:"tags"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func main() {
	fmt.Println("🎥 创建直播间演示")
	fmt.Println("==================")

	// 连接数据库
	dsn := "root:wida@tcp(localhost:3306)/livewin?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 1. 确保有用户存在
	var user User
	result := db.Where("username = ?", "wida").First(&user)
	if result.Error == gorm.ErrRecordNotFound {
		// 创建用户
		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("123456"), bcrypt.DefaultCost)
		user = User{
			Username: "wida",
			Email:    "<EMAIL>",
			Password: string(hashedPassword),
			Nickname: "wida",
			Level:    1,
			Coins:    1000,
			IsStreamer: true,
			Status:   "active",
		}
		if err := db.Create(&user).Error; err != nil {
			log.Fatal("创建用户失败:", err)
		}
		fmt.Printf("✅ 创建用户: %s\n", user.Username)
	} else {
		fmt.Printf("✅ 用户已存在: %s (ID: %d)\n", user.Username, user.ID)
	}

	// 2. 检查是否已有直播间
	var existingRoom Room
	if err := db.Where("user_id = ?", user.ID).First(&existingRoom).Error; err == nil {
		fmt.Printf("⚠️  用户已有直播间: %s (ID: %d)\n", existingRoom.Title, existingRoom.ID)
		
		// 显示直播间信息
		fmt.Println("\n📺 直播间信息:")
		fmt.Printf("   ID: %d\n", existingRoom.ID)
		fmt.Printf("   标题: %s\n", existingRoom.Title)
		fmt.Printf("   描述: %s\n", existingRoom.Description)
		fmt.Printf("   推流密钥: %s\n", existingRoom.StreamKey)
		fmt.Printf("   推流地址: %s\n", existingRoom.PushURL)
		fmt.Printf("   播放地址: %s\n", existingRoom.StreamURL)
		fmt.Printf("   是否直播中: %t\n", existingRoom.IsLive)
		fmt.Printf("   观看次数: %d\n", existingRoom.ViewCount)
		return
	}

	// 3. 创建新直播间
	streamKey := generateStreamKey()
	room := Room{
		UserID:      user.ID,
		Title:       "wida的精彩直播间",
		Description: "欢迎来到我的直播间！这里有最精彩的内容等着你！",
		StreamKey:   streamKey,
		Category:    "娱乐",
		Tags:        "聊天,游戏,音乐",
		IsLive:      false,
		ViewCount:   0,
	}

	// 生成直播地址
	room.PushURL = fmt.Sprintf("rtmp://localhost:1935/live/%s", streamKey)
	room.StreamURL = fmt.Sprintf("http://localhost:3001/wida/wida.m3u8")

	if err := db.Create(&room).Error; err != nil {
		log.Fatal("创建直播间失败:", err)
	}

	fmt.Printf("🎉 直播间创建成功!\n")
	fmt.Println("\n📺 直播间信息:")
	fmt.Printf("   ID: %d\n", room.ID)
	fmt.Printf("   标题: %s\n", room.Title)
	fmt.Printf("   描述: %s\n", room.Description)
	fmt.Printf("   推流密钥: %s\n", room.StreamKey)
	fmt.Printf("   推流地址: %s\n", room.PushURL)
	fmt.Printf("   播放地址: %s\n", room.StreamURL)
	fmt.Printf("   分类: %s\n", room.Category)
	fmt.Printf("   标签: %s\n", room.Tags)

	fmt.Println("\n🚀 下一步操作:")
	fmt.Printf("   1. 访问管理页面: http://localhost:3000/manage\n")
	fmt.Printf("   2. 访问直播间: http://localhost:3000/room/%d\n", room.ID)
	fmt.Printf("   3. 使用OBS推流: %s\n", room.PushURL)
	fmt.Printf("   4. 推流密钥: %s\n", room.StreamKey)
}

func generateStreamKey() string {
	// 生成基于时间戳的唯一推流密钥
	timestamp := time.Now().UnixNano()
	hash := md5.Sum([]byte(fmt.Sprintf("livewin_%d", timestamp)))
	return fmt.Sprintf("%x", hash)
}
