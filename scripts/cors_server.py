#!/usr/bin/env python3
"""
简单的CORS支持的HTTP服务器，用于提供HLS文件
"""

import http.server
import socketserver
import os
from urllib.parse import urlparse

class CORSRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        
        # 根据文件扩展名设置正确的Content-Type
        if self.path.endswith('.m3u8'):
            self.send_header('Content-Type', 'application/vnd.apple.mpegurl')
        elif self.path.endswith('.ts'):
            self.send_header('Content-Type', 'video/mp2t')
        
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 自定义日志格式
        print(f"[HLS Server] {format % args}")

def main():
    PORT = 8090
    
    # 确保在正确的目录中
    if not os.path.exists('hls'):
        print("❌ hls目录不存在，请确保在scripts目录中运行")
        return
    
    print(f"🎥 启动CORS支持的HLS服务器...")
    print(f"📡 端口: {PORT}")
    print(f"📂 目录: {os.getcwd()}")
    print(f"🌐 测试地址: http://localhost:{PORT}/hls/7136eabeeef5a624d15dfe93a25acaee.m3u8")
    print(f"🔍 健康检查: curl -v http://localhost:{PORT}/hls/7136eabeeef5a624d15dfe93a25acaee.m3u8")
    print("=" * 60)
    
    with socketserver.TCPServer(("", PORT), CORSRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()
