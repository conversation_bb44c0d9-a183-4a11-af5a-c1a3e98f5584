#!/bin/bash

# 停止开发环境脚本

echo "🛑 停止 LiveWin 开发环境..."

# 停止进程
if [ -f .backend.pid ]; then
    BACKEND_PID=$(cat .backend.pid)
    kill $BACKEND_PID 2>/dev/null && echo "✅ 后端服务已停止"
    rm -f .backend.pid
fi

if [ -f .chatroom.pid ]; then
    CHATROOM_PID=$(cat .chatroom.pid)
    kill $CHATROOM_PID 2>/dev/null && echo "✅ 聊天室服务已停止"
    rm -f .chatroom.pid
fi

if [ -f .frontend.pid ]; then
    FRONTEND_PID=$(cat .frontend.pid)
    kill $FRONTEND_PID 2>/dev/null && echo "✅ 前端服务已停止"
    rm -f .frontend.pid
fi

# 停止Docker容器（如果使用）
if command -v docker &> /dev/null; then
    docker stop livewin-postgres livewin-redis 2>/dev/null || true
    echo "✅ 数据库服务已停止"
fi

echo "🎉 开发环境已完全停止"
