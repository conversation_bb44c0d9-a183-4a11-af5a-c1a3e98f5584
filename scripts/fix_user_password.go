package main

import (
	"fmt"
	"log"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type User struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Username string `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email    string `json:"email" gorm:"uniqueIndex;size:100;not null"`
	Password string `json:"-" gorm:"not null"`
}

func main() {
	fmt.Println("🔧 修复用户密码哈希")
	fmt.Println("===================")

	// 连接数据库
	dsn := "root:wida@tcp(localhost:3306)/livewin?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 为demo_streamer用户生成正确的密码哈希
	password := "password123"
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("生成密码哈希失败:", err)
	}

	fmt.Printf("📝 为用户 demo_streamer 生成新的密码哈希\n")
	fmt.Printf("   原始密码: %s\n", password)
	fmt.Printf("   哈希值: %s\n", string(hashedPassword))

	// 更新数据库中的密码
	result := db.Model(&User{}).Where("username = ?", "demo_streamer").Update("password", string(hashedPassword))
	if result.Error != nil {
		log.Fatal("更新密码失败:", result.Error)
	}

	if result.RowsAffected == 0 {
		fmt.Println("❌ 未找到用户 demo_streamer")
		return
	}

	fmt.Printf("✅ 成功更新用户密码 (影响行数: %d)\n", result.RowsAffected)

	// 验证更新
	var user User
	if err := db.Where("username = ?", "demo_streamer").First(&user).Error; err != nil {
		log.Fatal("验证用户失败:", err)
	}

	// 测试密码验证
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password))
	if err != nil {
		fmt.Println("❌ 密码验证失败:", err)
	} else {
		fmt.Println("✅ 密码验证成功")
	}

	fmt.Println("\n🎉 密码修复完成!")
	fmt.Println("现在可以使用以下凭据登录:")
	fmt.Printf("   用户名: demo_streamer\n")
	fmt.Printf("   密码: %s\n", password)
}
