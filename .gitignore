# 根目录 .gitignore

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# Docker 相关
docker-compose.override.yml

# 脚本执行日志
scripts/*.log

# 测试文件（如果是临时的）
test-*.html
test-*.js

# 各模块的构建产物和依赖
backend/tmp/
backend/*.exe
backend/*.dll
backend/*.so
backend/*.dylib
backend/vendor/
backend/main
backend/app
backend/server

chatroom/tmp/
chatroom/*.exe
chatroom/*.dll
chatroom/*.so
chatroom/*.dylib
chatroom/vendor/
chatroom/main
chatroom/app
chatroom/server

frontend/node_modules/
frontend/build/
frontend/dist/
frontend/.next/
frontend/out/
frontend/.nuxt/
frontend/.vuepress/dist/
frontend/.serverless/
frontend/.fusebox/
frontend/.dynamodb/
frontend/.tern-port
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/lerna-debug.log*
frontend/.pnpm-debug.log*
frontend/.npm
frontend/.eslintcache
frontend/.stylelintcache
frontend/.cache/
frontend/.parcel-cache/
frontend/.nyc_output/
frontend/coverage/
frontend/lib-cov/
frontend/.grunt/
frontend/bower_components/
frontend/.lock-wscript
frontend/build/Release/
frontend/node_modules.tar.gz
frontend/Thumbs.db
frontend/ehthumbs.db
frontend/Desktop.ini
frontend/$RECYCLE.BIN/
frontend/*.cab
frontend/*.msi
frontend/*.msix
frontend/*.msm
frontend/*.msp
frontend/*.lnk

# 部署相关
deploy/
deployment/
k8s/secrets/
*.pem
*.key
*.crt
*.p12

# 监控和日志
monitoring/
grafana/data/
prometheus/data/
