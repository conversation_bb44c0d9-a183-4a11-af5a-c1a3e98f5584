# LiveWin 直播网站

一个功能完整的直播网站，包含前端界面、后端API服务和实时聊天室功能。

## 项目结构

```
livewin/
├── frontend/          # 前端模块 (React + TypeScript)
│   ├── src/
│   ├── public/
│   └── package.json
├── backend/           # 后端API模块 (Go)
│   ├── cmd/
│   ├── internal/
│   ├── pkg/
│   └── go.mod
├── chatroom/          # 聊天室模块 (Go + WebSocket)
│   ├── cmd/
│   ├── internal/
│   ├── pkg/
│   └── go.mod
├── docker-compose.yml # Docker编排文件
└── README.md
```

## 功能特性

### 前端功能
- 🎥 直播视频播放
- 💬 实时弹幕系统
- 🎁 礼物系统
- 👤 用户认证
- 📱 响应式设计

### 后端功能
- 🔐 用户认证与授权
- 📺 直播间管理
- 🎁 礼物系统API
- 📊 数据统计
- 🗄️ 数据库管理

### 聊天室功能
- ⚡ WebSocket实时通信
- 💬 实时弹幕
- 🎁 礼物特效
- 👥 在线用户管理
- 🚫 消息过滤

## 技术栈

- **前端**: React, TypeScript, WebSocket, CSS3
- **后端**: Go, Gin, GORM, JWT
- **聊天室**: Go, Gorilla WebSocket
- **数据库**: PostgreSQL, Redis
- **部署**: Docker, Docker Compose

## 快速开始

### 环境要求
- Go 1.19+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (推荐)

### 方式一：使用 Docker (推荐)

1. 克隆项目
```bash
git clone <repository-url>
cd livewin
```

2. 一键启动
```bash
./scripts/start.sh
```

3. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:8080
- 聊天室: http://localhost:8081

### 方式二：开发环境

1. 克隆项目
```bash
git clone <repository-url>
cd livewin
```

2. 启动开发环境
```bash
./scripts/dev.sh
```

3. 停止开发环境
```bash
./scripts/stop-dev.sh
```

### 方式三：手动启动

1. 启动数据库
```bash
# PostgreSQL
docker run -d --name livewin-postgres \
  -e POSTGRES_DB=livewin \
  -e POSTGRES_USER=livewin \
  -e POSTGRES_PASSWORD=password \
  -p 5432:5432 postgres:13

# Redis
docker run -d --name livewin-redis \
  -p 6379:6379 redis:6-alpine
```

2. 启动后端服务
```bash
cd backend
go mod tidy
go run cmd/main.go
```

3. 启动聊天室服务
```bash
cd chatroom
go mod tidy
go run cmd/main.go
```

4. 启动前端服务
```bash
cd frontend
npm install
npm start
```

## 开发指南

详细的开发文档请参考各模块的README文件：
- [后端开发指南](./backend/README.md)
- [聊天室开发指南](./chatroom/README.md)
- [前端开发指南](./frontend/README.md)

## 安全说明

### 依赖安全状态
- ✅ **已修复**: 11个高危和中危安全漏洞
- ⚠️ **剩余**: 3个中等严重性漏洞（仅影响开发环境）
- 📋 **详情**: 查看 [frontend/SECURITY.md](./frontend/SECURITY.md)

### 安全建议
- 开发环境：仅在可信网络中运行，使用现代浏览器
- 生产环境：使用 `npm run build` 构建，配置 HTTPS 和安全头
- 定期运行 `npm audit` 检查安全状态

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :3000
   lsof -i :8080
   lsof -i :8081

   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   docker ps | grep postgres

   # 重启数据库
   docker restart livewin-postgres
   ```

3. **前端依赖问题**
   ```bash
   cd frontend
   rm -rf node_modules package-lock.json
   npm install --legacy-peer-deps
   ```

4. **Go模块问题**
   ```bash
   cd backend  # 或 chatroom
   go mod tidy
   go mod download
   ```

## 许可证

MIT License


## 架构图

```mermaid
graph TB
    subgraph "前端层"
        A[React 前端应用]
        A1[视频播放器]
        A2[聊天室组件]
        A3[礼物系统]
        A4[用户界面]
    end
    
    subgraph "API网关层"
        B[Nginx 反向代理]
    end
    
    subgraph "应用服务层"
        C[后端API服务<br/>Go + Gin]
        D[聊天室服务<br/>Go + WebSocket]
    end
    
    subgraph "数据存储层"
        E[PostgreSQL<br/>主数据库]
        F[Redis<br/>缓存/会话]
    end
    
    subgraph "外部服务"
        G[直播推流服务<br/>RTMP/HLS]
        H[文件存储服务<br/>头像/封面]
    end
    
    A --> B
    A1 --> G
    A2 --> D
    A3 --> C
    A4 --> C
    
    B --> C
    B --> D
    
    C --> E
    C --> F
    D --> F
    
    C --> H
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#fce4ec
    style H fill:#fce4ec
```


数据流图：

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端应用
    participant B as 后端API
    participant C as 聊天室服务
    participant DB as 数据库
    participant R as Redis
    
    Note over U,R: 用户注册登录流程
    U->>F: 注册/登录
    F->>B: POST /auth/register|login
    B->>DB: 验证用户信息
    DB-->>B: 返回用户数据
    B->>R: 缓存用户会话
    B-->>F: 返回JWT Token
    F-->>U: 登录成功
    
    Note over U,R: 进入直播间流程
    U->>F: 访问直播间
    F->>B: GET /room/:id
    B->>DB: 查询房间信息
    DB-->>B: 返回房间数据
    B-->>F: 房间信息
    F->>C: WebSocket连接
    C->>R: 记录在线用户
    C-->>F: 连接成功
    F-->>U: 显示直播间
    
    Note over U,R: 发送弹幕流程
    U->>F: 发送弹幕
    F->>C: WebSocket消息
    C->>R: 消息过滤验证
    C->>C: 广播给房间用户
    C-->>F: 弹幕消息
    F-->>U: 显示弹幕
    
    Note over U,R: 发送礼物流程
    U->>F: 选择礼物
    F->>B: POST /gift/send
    B->>DB: 扣除用户金币
    B->>DB: 记录礼物记录
    B->>DB: 增加主播收益
    DB-->>B: 操作成功
    B->>C: 通知礼物消息
    C->>C: 广播礼物特效
    C-->>F: 礼物消息
    B-->>F: 发送成功
    F-->>U: 显示礼物特效
```