version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: livewin
      POSTGRES_USER: livewin
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=*****************************************/livewin?sslmode=disable
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - PORT=8080
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    working_dir: /app

  chatroom:
    build:
      context: ./chatroom
      dockerfile: Dockerfile
    ports:
      - "8081:8081"
    environment:
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-jwt-secret-key
      - CHATROOM_PORT=8081
    depends_on:
      - redis
    volumes:
      - ./chatroom:/app
    working_dir: /app

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_CHATROOM_URL=ws://localhost:8081
    volumes:
      - ./frontend:/app
      - /app/node_modules
    working_dir: /app

volumes:
  postgres_data:
  redis_data:
