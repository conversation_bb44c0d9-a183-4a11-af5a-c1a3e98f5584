# LiveWin 前端应用

基于 React + TypeScript 开发的直播平台前端应用。

## 功能特性

- 🎥 直播视频播放
- 💬 实时弹幕系统
- 🎁 礼物系统
- 👤 用户认证
- 📱 响应式设计
- 🎨 Material-UI 界面

## 技术栈

- **框架**: React 18
- **语言**: TypeScript
- **UI库**: Material-UI (MUI)
- **路由**: React Router
- **HTTP客户端**: Axios
- **视频播放**: Video.js
- **WebSocket**: 原生 WebSocket API

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── VideoPlayer.tsx  # 视频播放器
│   ├── ChatRoom.tsx     # 聊天室
│   └── GiftPanel.tsx    # 礼物面板
├── pages/              # 页面组件
│   ├── HomePage.tsx     # 首页
│   ├── LiveRoomPage.tsx # 直播间页面
│   ├── LoginPage.tsx    # 登录页面
│   ├── RegisterPage.tsx # 注册页面
│   └── ProfilePage.tsx  # 个人资料页面
├── services/           # 服务层
│   ├── api.ts          # API 接口
│   └── websocket.ts    # WebSocket 服务
├── App.tsx             # 主应用组件
└── index.tsx           # 应用入口
```

## 主要组件

### VideoPlayer
视频播放器组件，支持：
- HLS 直播流播放
- 播放控制
- 全屏模式
- 响应式布局

### ChatRoom
聊天室组件，支持：
- 实时消息显示
- 消息发送
- 用户信息显示
- 礼物消息特殊样式

### GiftPanel
礼物面板组件，支持：
- 礼物列表展示
- 礼物选择和发送
- 数量选择
- 留言功能

## 页面说明

### HomePage
- 显示正在直播的房间列表
- 房间信息卡片
- 分类筛选
- 搜索功能

### LiveRoomPage
- 视频播放区域
- 实时聊天室
- 礼物系统
- 房间信息展示

### LoginPage / RegisterPage
- 用户登录注册
- 表单验证
- 错误提示

### ProfilePage
- 用户信息展示
- 个人资料编辑
- 礼物历史

## API 集成

### 认证
```typescript
// 登录
const response = await login(username, password);
localStorage.setItem('token', response.data.token);

// 注册
const response = await register(username, email, password);
```

### 房间管理
```typescript
// 获取房间列表
const rooms = await getRooms(page, limit);

// 获取房间详情
const room = await getRoom(roomId);
```

### 礼物系统
```typescript
// 获取礼物列表
const gifts = await getGifts();

// 发送礼物
await sendGift({
  room_id: roomId,
  gift_id: giftId,
  count: count,
  message: message
});
```

## WebSocket 集成

### 连接聊天室
```typescript
const ws = connectWebSocket(
  roomId,
  userId,
  username,
  nickname,
  avatar
);

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  setMessages(prev => [...prev, message]);
};
```

### 发送消息
```typescript
sendMessage(ws, 'chat', messageContent);
```

## 环境配置

### 环境变量
```bash
REACT_APP_API_URL=http://localhost:8080
REACT_APP_CHATROOM_URL=ws://localhost:8081
```

### 开发环境
```bash
npm install
npm start
```

### 生产构建
```bash
npm run build
```

## 样式主题

使用 Material-UI 的深色主题：

```typescript
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#ff6b6b',
    },
    secondary: {
      main: '#4ecdc4',
    },
  },
});
```

## 响应式设计

- 移动端适配
- 平板端适配
- 桌面端优化
- 弹性布局

## 部署

### Nginx 配置
```nginx
server {
    listen 80;
    root /usr/share/nginx/html;
    
    # React Router 支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://backend:8080;
    }
    
    # WebSocket 代理
    location /ws {
        proxy_pass http://chatroom:8081;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### Docker 部署
```bash
docker build -t livewin-frontend .
docker run -p 3000:80 livewin-frontend
```
