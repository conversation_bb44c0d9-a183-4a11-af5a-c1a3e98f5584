# React/Node.js 前端 .gitignore

# 依赖
node_modules/
/.pnp
.pnp.js

# 测试
/coverage

# 生产构建
/build
/dist
/.next/
/out/

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 目录用于检测工具
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 stylelint 缓存
.stylelintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# yarn v2 的输出
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next
out

# Nuxt.js 构建/生成输出
.nuxt
dist

# Gatsby 文件
.cache/

# Vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# Stores VSCode 版本用于测试
.vscode-test

# yarn v1 错误日志
yarn-error.log

# 调试日志
npm-debug.log*
yarn-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 可选的 npm 缓存目录
.npm

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 热重载
.hot-update.*

# Webpack 包分析
webpack-bundle-analyzer-report.html

# Storybook 构建输出
storybook-static

# 备份文件
*.bak
*.backup
