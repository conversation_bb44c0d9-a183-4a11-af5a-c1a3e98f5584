import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, Paper, Chip, Button } from '@mui/material';
import { PlayArrow, Pause, VolumeUp, Fullscreen, LiveTv } from '@mui/icons-material';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';

interface VideoPlayerProps {
  roomId: number;
  isLive: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ roomId, isLive }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [currentTime, setCurrentTime] = useState(0);

  // 模拟直播时间更新
  useEffect(() => {
    if (isLive && isPlaying) {
      const interval = setInterval(() => {
        setCurrentTime(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isLive, isPlaying]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVolume(parseInt(event.target.value));
  };

  if (!isLive) {
    return (
      <Paper
        sx={{
          width: '100%',
          height: '400px',
          backgroundColor: '#1a1a1a',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          position: 'relative',
          backgroundImage: 'linear-gradient(45deg, #1a1a1a 25%, #2a2a2a 25%, #2a2a2a 50%, #1a1a1a 50%, #1a1a1a 75%, #2a2a2a 75%, #2a2a2a)',
          backgroundSize: '20px 20px',
        }}
      >
        <LiveTv sx={{ fontSize: 80, mb: 2, opacity: 0.5 }} />
        <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
          主播暂未开播
        </Typography>
        <Typography variant="body1" sx={{ opacity: 0.7 }}>
          请稍后再来观看
        </Typography>
      </Paper>
    );
  }

  // 模拟直播界面
  return (
    <Paper sx={{ width: '100%', position: 'relative', backgroundColor: '#000' }}>
      {/* 模拟视频内容 */}
      <Box
        sx={{
          width: '100%',
          height: '400px',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {/* 模拟视频内容 */}
        <Box
          sx={{
            textAlign: 'center',
            color: 'white',
            zIndex: 2,
          }}
        >
          <LiveTv sx={{ fontSize: 100, mb: 2, opacity: 0.8 }} />
          <Typography variant="h4" sx={{ mb: 1, fontWeight: 700 }}>
            直播中
          </Typography>
          <Typography variant="h6" sx={{ opacity: 0.9 }}>
            房间 #{roomId}
          </Typography>
        </Box>

        {/* 直播状态指示器 */}
        <Chip
          icon={<LiveTv />}
          label="LIVE"
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: '#ef4444',
            color: 'white',
            fontWeight: 700,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 },
            },
          }}
        />

        {/* 观看时长 */}
        <Paper
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            px: 2,
            py: 1,
            backgroundColor: 'rgba(0,0,0,0.7)',
            color: 'white',
          }}
        >
          <Typography variant="body2" fontWeight={600}>
            {formatTime(currentTime)}
          </Typography>
        </Paper>

        {/* 播放/暂停覆盖层 */}
        {!isPlaying && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0,0,0,0.3)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 3,
            }}
          >
            <Button
              onClick={handlePlayPause}
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.9)',
                color: '#333',
                '&:hover': {
                  backgroundColor: 'white',
                },
              }}
            >
              <PlayArrow sx={{ fontSize: 40 }} />
            </Button>
          </Box>
        )}
      </Box>

      {/* 控制栏 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
          p: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Button
          onClick={handlePlayPause}
          sx={{ color: 'white', minWidth: 'auto' }}
        >
          {isPlaying ? <Pause /> : <PlayArrow />}
        </Button>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <VolumeUp sx={{ color: 'white', fontSize: 20 }} />
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={handleVolumeChange}
            style={{
              width: '80px',
              height: '4px',
              background: '#666',
              outline: 'none',
              borderRadius: '2px',
            }}
          />
        </Box>

        <Box sx={{ flexGrow: 1 }} />

        <Typography variant="body2" sx={{ color: 'white', opacity: 0.8 }}>
          {isPlaying ? '正在播放' : '已暂停'}
        </Typography>

        <Button sx={{ color: 'white', minWidth: 'auto' }}>
          <Fullscreen />
        </Button>
      </Box>
    </Paper>
  );
};

export default VideoPlayer;
