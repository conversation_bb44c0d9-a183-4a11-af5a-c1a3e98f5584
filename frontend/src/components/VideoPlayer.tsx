import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, Paper, Chip, Button, Alert } from '@mui/material';
import { PlayArrow, Pause, VolumeUp, Fullscreen, LiveTv } from '@mui/icons-material';
import videojs from 'video.js';
import 'video.js/dist/video-js.css';
import { getStreamInfo } from '../services/api';

interface VideoPlayerProps {
  roomId: number;
  isLive: boolean;
}

interface StreamInfo {
  room_id: number;
  is_live: boolean;
  stream_url?: string;
  push_url?: string;
  stream_key?: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ roomId, isLive }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(80);
  const [currentTime, setCurrentTime] = useState(0);
  const [streamInfo, setStreamInfo] = useState<StreamInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [demoViewers, setDemoViewers] = useState(1234);
  const [demoLikes, setDemoLikes] = useState(5678);

  // 获取直播流信息
  useEffect(() => {
    const fetchStreamInfo = async () => {
      try {
        setLoading(true);
        const response = await getStreamInfo(roomId);
        setStreamInfo(response.data.stream_info);
        setError('');
      } catch (err: any) {
        console.error('Failed to get stream info:', err);
        setError('获取直播信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchStreamInfo();
  }, [roomId]);

  // 模拟直播时间更新
  useEffect(() => {
    if (isLive && isPlaying) {
      const interval = setInterval(() => {
        setCurrentTime(prev => prev + 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [isLive, isPlaying]);

  // 演示模式数据动态更新
  useEffect(() => {
    if (error || !streamInfo?.stream_url) {
      const interval = setInterval(() => {
        // 随机增加观看人数
        setDemoViewers(prev => prev + Math.floor(Math.random() * 5));

        // 随机增加点赞数
        if (Math.random() > 0.7) {
          setDemoLikes(prev => prev + Math.floor(Math.random() * 10 + 1));
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [error, streamInfo]);

  // 初始化视频播放器
  useEffect(() => {
    if (streamInfo?.stream_url && isLive && videoRef.current) {
      console.log('Initializing video player with stream URL:', streamInfo.stream_url);

      // 清理之前的播放器
      if (playerRef.current) {
        playerRef.current.dispose();
        playerRef.current = null;
      }

      // 检查流是否可用
      const checkStreamAvailability = async () => {
        try {
          if (!streamInfo.stream_url) {
            throw new Error('Stream URL is not available');
          }
          const response = await fetch(streamInfo.stream_url, { method: 'HEAD' });
          if (!response.ok) {
            throw new Error(`Stream not available: ${response.status}`);
          }
          return true;
        } catch (err) {
          console.warn('Stream check failed:', err);
          return false;
        }
      };

      checkStreamAvailability().then(isAvailable => {
        if (!isAvailable) {
          console.log('Stream not available, showing demo mode');
          setError('直播流暂时不可用，显示演示模式');
          return;
        }

        try {
          // 检查videoRef是否可用
          if (!videoRef.current) {
            throw new Error('Video element is not available');
          }

          // 使用Video.js初始化HLS播放器
          const player = videojs(videoRef.current, {
            controls: true,
            responsive: true,
            fluid: true,
            liveui: true,
            sources: [{
              src: streamInfo.stream_url,
              type: 'application/x-mpegURL'
            }],
            html5: {
              hls: {
                enableLowInitialPlaylist: true,
                smoothQualityChange: true,
                overrideNative: true
              }
            },
            // 添加错误恢复选项
            techOrder: ['html5'],
            autoplay: false,
            muted: false
          });

          // 添加错误处理
          player.on('error', (e: any) => {
            console.error('Video player error:', e);
            console.log('Falling back to demo mode due to stream error');
            setError('视频播放出现问题，切换到演示模式');

            // 清理播放器
            if (playerRef.current) {
              try {
                playerRef.current.dispose();
              } catch (err) {
                console.error('Error disposing player:', err);
              }
              playerRef.current = null;
            }
          });

          // 添加加载事件
          player.on('loadstart', () => {
            console.log('Video loading started');
            setError(''); // 清除错误信息
          });

          player.on('canplay', () => {
            console.log('Video can play');
            setError(''); // 清除错误信息
          });

          player.on('waiting', () => {
            console.log('Video waiting for data');
          });

          player.on('playing', () => {
            console.log('Video is playing');
            setError(''); // 清除错误信息
          });

          playerRef.current = player;
        } catch (err) {
          console.error('Failed to initialize video player:', err);
          setError('视频播放器初始化失败，显示演示模式');
        }
      });
    }

    return () => {
      if (playerRef.current) {
        try {
          playerRef.current.dispose();
        } catch (err) {
          console.error('Error disposing video player:', err);
        }
        playerRef.current = null;
      }
    };
  }, [streamInfo, isLive]);

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setVolume(parseInt(event.target.value));
  };

  // 加载状态
  if (loading) {
    return (
      <Paper
        sx={{
          width: '100%',
          height: '400px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
        }}
      >
        <Typography>加载直播信息中...</Typography>
      </Paper>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Paper sx={{ width: '100%', p: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Paper>
    );
  }

  if (!isLive) {
    return (
      <Paper
        sx={{
          width: '100%',
          height: '400px',
          backgroundColor: '#1a1a1a',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          position: 'relative',
          backgroundImage: 'linear-gradient(45deg, #1a1a1a 25%, #2a2a2a 25%, #2a2a2a 50%, #1a1a1a 50%, #1a1a1a 75%, #2a2a2a 75%, #2a2a2a)',
          backgroundSize: '20px 20px',
        }}
      >
        <LiveTv sx={{ fontSize: 80, mb: 2, opacity: 0.5 }} />
        <Typography variant="h5" sx={{ mb: 1, fontWeight: 600 }}>
          主播暂未开播
        </Typography>
        <Typography variant="body1" sx={{ opacity: 0.7 }}>
          请稍后再来观看
        </Typography>
      </Paper>
    );
  }

  // 直播界面
  return (
    <Paper sx={{ width: '100%', position: 'relative', backgroundColor: '#000' }}>
      {/* 视频播放器容器 */}
      <Box
        sx={{
          width: '100%',
          height: '400px',
          position: 'relative',
          overflow: 'hidden',
          backgroundColor: '#000',
        }}
      >
        {streamInfo?.stream_url ? (
          <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
            {/* 真实的视频播放器 */}
            <video
              ref={videoRef}
              className="video-js vjs-default-skin"
              controls
              preload="auto"
              width="100%"
              height="400"
              data-setup="{}"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain',
              }}
            />

            {/* 调试信息 */}
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                backgroundColor: 'rgba(0,0,0,0.7)',
                color: 'white',
                padding: '4px 8px',
                borderRadius: 1,
                fontSize: '0.75rem',
                fontFamily: 'monospace',
                maxWidth: '300px',
                wordBreak: 'break-all'
              }}
            >
              {streamInfo.stream_url}
            </Box>
          </Box>
        ) : (
          // 备用显示内容 - 演示直播画面
          <Box
            sx={{
              width: '100%',
              height: '100%',
              background: 'linear-gradient(45deg, #1e3c72 0%, #2a5298 50%, #1e3c72 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            {/* 动态背景效果 */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `
                  radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                  radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)
                `,
                animation: 'pulse 4s ease-in-out infinite',
              }}
            />

            {/* 主要内容 */}
            <Box
              sx={{
                textAlign: 'center',
                color: 'white',
                zIndex: 2,
                position: 'relative',
              }}
            >
              <LiveTv sx={{ fontSize: 120, mb: 3, opacity: 0.9, filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.3))' }} />
              <Typography variant="h3" sx={{ mb: 2, fontWeight: 700, textShadow: '0 2px 4px rgba(0,0,0,0.5)' }}>
                直播演示
              </Typography>
              <Typography variant="h5" sx={{ mb: 3, opacity: 0.9, textShadow: '0 1px 2px rgba(0,0,0,0.5)' }}>
                房间 #{roomId} - 我的精彩直播间
              </Typography>

              {/* 模拟直播信息 */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: 4,
                mb: 3,
                flexWrap: 'wrap'
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>观看人数</Typography>
                  <Typography variant="h4" sx={{ color: '#ff6b6b' }}>{demoViewers.toLocaleString()}</Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>直播时长</Typography>
                  <Typography variant="h4" sx={{ color: '#4ecdc4' }}>
                    {Math.floor(currentTime / 3600).toString().padStart(2, '0')}:
                    {Math.floor((currentTime % 3600) / 60).toString().padStart(2, '0')}:
                    {(currentTime % 60).toString().padStart(2, '0')}
                  </Typography>
                </Box>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>点赞数</Typography>
                  <Typography variant="h4" sx={{ color: '#ffe66d' }}>{demoLikes.toLocaleString()}</Typography>
                </Box>
              </Box>

              <Typography variant="body1" sx={{ opacity: 0.8, maxWidth: 600, mx: 'auto', mb: 2 }}>
                这是一个演示直播间。在真实环境中，这里会显示来自推流的实时视频内容。
                {streamInfo?.stream_url && (
                  <>
                    <br />
                    <br />
                    当前配置的流地址: {streamInfo.stream_url}
                  </>
                )}
              </Typography>

              {/* 错误信息和重试按钮 */}
              {error && (
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Alert severity="warning" sx={{ mb: 2, maxWidth: 500, mx: 'auto' }}>
                    {error}
                  </Alert>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      setError('');
                      // 重新获取流信息
                      const fetchStreamInfo = async () => {
                        try {
                          setLoading(true);
                          const response = await getStreamInfo(roomId);
                          setStreamInfo(response.data.stream_info);
                          setError('');
                        } catch (err: any) {
                          console.error('Failed to get stream info:', err);
                          setError('获取直播信息失败');
                        } finally {
                          setLoading(false);
                        }
                      };
                      fetchStreamInfo();
                    }}
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      '&:hover': { backgroundColor: 'rgba(255,255,255,0.3)' }
                    }}
                  >
                    重试连接
                  </Button>
                </Box>
              )}
            </Box>

            {/* CSS动画样式 */}
            <style>
              {`
                @keyframes pulse {
                  0%, 100% { opacity: 1; }
                  50% { opacity: 0.7; }
                }
              `}
            </style>
          </Box>
        )}

        {/* 直播状态指示器 */}
        <Chip
          icon={<LiveTv />}
          label="LIVE"
          sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: '#ef4444',
            color: 'white',
            fontWeight: 700,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 },
            },
          }}
        />

        {/* 观看时长 */}
        <Paper
          sx={{
            position: 'absolute',
            top: 16,
            right: 16,
            px: 2,
            py: 1,
            backgroundColor: 'rgba(0,0,0,0.7)',
            color: 'white',
          }}
        >
          <Typography variant="body2" fontWeight={600}>
            {formatTime(currentTime)}
          </Typography>
        </Paper>

        {/* 播放/暂停覆盖层 */}
        {!isPlaying && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0,0,0,0.3)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 3,
            }}
          >
            <Button
              onClick={handlePlayPause}
              sx={{
                width: 80,
                height: 80,
                borderRadius: '50%',
                backgroundColor: 'rgba(255,255,255,0.9)',
                color: '#333',
                '&:hover': {
                  backgroundColor: 'white',
                },
              }}
            >
              <PlayArrow sx={{ fontSize: 40 }} />
            </Button>
          </Box>
        )}
      </Box>

      {/* 控制栏 */}
      <Box
        sx={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
          p: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 2,
        }}
      >
        <Button
          onClick={handlePlayPause}
          sx={{ color: 'white', minWidth: 'auto' }}
        >
          {isPlaying ? <Pause /> : <PlayArrow />}
        </Button>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <VolumeUp sx={{ color: 'white', fontSize: 20 }} />
          <input
            type="range"
            min="0"
            max="100"
            value={volume}
            onChange={handleVolumeChange}
            style={{
              width: '80px',
              height: '4px',
              background: '#666',
              outline: 'none',
              borderRadius: '2px',
            }}
          />
        </Box>

        <Box sx={{ flexGrow: 1 }} />

        <Typography variant="body2" sx={{ color: 'white', opacity: 0.8 }}>
          {isPlaying ? '正在播放' : '已暂停'}
        </Typography>

        <Button sx={{ color: 'white', minWidth: 'auto' }}>
          <Fullscreen />
        </Button>
      </Box>
    </Paper>
  );
};

export default VideoPlayer;
