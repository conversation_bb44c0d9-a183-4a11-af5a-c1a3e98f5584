import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Paper,
  IconButton,
} from '@mui/material';
import { Add, Remove } from '@mui/icons-material';
import { getGifts, sendGift } from '../services/api';
import { sendGiftMessage } from '../services/websocket';

interface Gift {
  id: number;
  name: string;
  icon: string;
  price: number;
  effect: string;
  category: string;
}

interface GiftPanelProps {
  roomId: number;
  websocket?: WebSocket;
}

const GiftPanel: React.FC<GiftPanelProps> = ({ roomId, websocket }) => {
  const [gifts, setGifts] = useState<Gift[]>([]);
  const [selectedGift, setSelectedGift] = useState<Gift | null>(null);
  const [giftCount, setGiftCount] = useState(1);
  const [message, setMessage] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [sending, setSending] = useState(false);

  useEffect(() => {
    loadGifts();
  }, []);

  const loadGifts = async () => {
    try {
      const response = await getGifts();
      setGifts(response.data.gifts || []);
    } catch (error) {
      console.error('Failed to load gifts:', error);
    }
  };

  const handleGiftClick = (gift: Gift) => {
    setSelectedGift(gift);
    setGiftCount(1);
    setMessage('');
    setDialogOpen(true);
  };

  const handleSendGift = async () => {
    if (!selectedGift) return;

    setSending(true);
    try {
      // 先调用HTTP API发送礼物
      await sendGift({
        room_id: roomId,
        gift_id: selectedGift.id,
        count: giftCount,
        message: message,
      });

      // 如果有WebSocket连接，发送礼物消息到聊天室
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        sendGiftMessage(
          websocket,
          {
            gift_id: selectedGift.id.toString(),
            gift_name: selectedGift.name,
            gift_icon: selectedGift.icon,
            gift_price: selectedGift.price,
            count: giftCount,
            effect: selectedGift.effect,
          },
          message
        );
      }

      setDialogOpen(false);
      // 这里可以显示成功消息或特效
    } catch (error: any) {
      console.error('Failed to send gift:', error);
      // 这里可以显示错误消息
    } finally {
      setSending(false);
    }
  };

  const handleCountChange = (delta: number) => {
    const newCount = giftCount + delta;
    if (newCount >= 1 && newCount <= 999) {
      setGiftCount(newCount);
    }
  };

  const totalCost = selectedGift ? selectedGift.price * giftCount : 0;

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        礼物
      </Typography>
      
      <Grid container spacing={1}>
        {gifts.map((gift) => (
          <Grid item xs={4} key={gift.id}>
            <Paper
              sx={{
                p: 1,
                textAlign: 'center',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
              onClick={() => handleGiftClick(gift)}
            >
              <Typography variant="h6">{gift.icon}</Typography>
              <Typography variant="caption" display="block">
                {gift.name}
              </Typography>
              <Chip
                label={`${gift.price}币`}
                size="small"
                color="primary"
                variant="outlined"
              />
            </Paper>
          </Grid>
        ))}
      </Grid>

      {/* 礼物发送对话框 */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          发送礼物
        </DialogTitle>
        <DialogContent>
          {selectedGift && (
            <Box>
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h3">{selectedGift.icon}</Typography>
                <Typography variant="h6">{selectedGift.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedGift.price} 金币/个
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                <IconButton onClick={() => handleCountChange(-1)} disabled={giftCount <= 1}>
                  <Remove />
                </IconButton>
                <Typography variant="h6" sx={{ mx: 2, minWidth: '60px', textAlign: 'center' }}>
                  {giftCount}
                </Typography>
                <IconButton onClick={() => handleCountChange(1)} disabled={giftCount >= 999}>
                  <Add />
                </IconButton>
              </Box>

              <TextField
                fullWidth
                label="留言 (可选)"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                multiline
                rows={2}
                sx={{ mb: 2 }}
              />

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h6" color="primary">
                  总计: {totalCost} 金币
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            取消
          </Button>
          <Button
            onClick={handleSendGift}
            variant="contained"
            disabled={sending}
          >
            {sending ? '发送中...' : '发送礼物'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default GiftPanel;
