import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  Avatar,
  Chip,
  Paper,
} from '@mui/material';
import { Send } from '@mui/icons-material';
import { connectWebSocket, sendMessage } from '../services/websocket';

interface Message {
  id: string;
  type: 'chat' | 'gift' | 'join' | 'leave' | 'system';
  user_id: string;
  username: string;
  nickname: string;
  avatar: string;
  level: number;
  content: string;
  timestamp: string;
  extra?: any;
}

interface ChatRoomProps {
  roomId: number;
  onWebSocketReady?: (ws: WebSocket) => void;
}

const ChatRoom: React.FC<ChatRoomProps> = ({ roomId, onWebSocketReady }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [connected, setConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    let mounted = true;

    const initConnection = () => {
      if (!mounted) return;

      const user = JSON.parse(localStorage.getItem('user') || '{}');
      console.log('User from localStorage:', user);

      const userToConnect = user.id ? user : {
        id: 2,
        username: 'wida',
        nickname: 'wida',
        avatar: ''
      };

      console.log('Connecting with user:', userToConnect);
      connectToChat(userToConnect);
    };

    // 延迟一下确保组件完全挂载
    const timer = setTimeout(initConnection, 100);

    return () => {
      mounted = false;
      clearTimeout(timer);
      console.log('Cleaning up WebSocket connection');
      if (wsRef.current) {
        wsRef.current.onclose = null; // 防止触发重连
        wsRef.current.close();
        wsRef.current = null;
      }
      if (heartbeatRef.current) {
        clearInterval(heartbeatRef.current);
        heartbeatRef.current = null;
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, []); // 只在组件挂载时连接一次

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const connectToChat = (user: any) => {
    // 防止重复连接
    if (wsRef.current && wsRef.current.readyState !== WebSocket.CLOSED) {
      console.log('WebSocket already exists and not closed, skipping connection');
      return;
    }

    // 清理之前的连接和定时器
    if (wsRef.current) {
      console.log('Closing existing WebSocket connection');
      wsRef.current.onclose = null; // 防止触发重连
      wsRef.current.close(1000, 'Reconnecting');
      wsRef.current = null;
    }
    if (heartbeatRef.current) {
      clearInterval(heartbeatRef.current);
      heartbeatRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    console.log('Creating new WebSocket connection...');
    const ws = connectWebSocket(
      roomId.toString(),
      user.id.toString(),
      user.username,
      user.nickname || user.username,
      user.avatar || ''
    );

    ws.onopen = () => {
      setConnected(true);
      console.log('WebSocket connected at', new Date().toLocaleTimeString());

      // 启动心跳检测
      heartbeatRef.current = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          console.log('Sending heartbeat ping');
          ws.send(JSON.stringify({ type: 'ping' }));
        }
      }, 30000); // 每30秒发送一次心跳

      // 通知父组件WebSocket已准备好
      if (onWebSocketReady) {
        onWebSocketReady(ws);
      }
    };

    ws.onmessage = (event) => {
      console.log('Received WebSocket message at', new Date().toLocaleTimeString(), ':', event.data);
      try {
        const message = JSON.parse(event.data);
        console.log('Parsed message:', message);

        // 忽略心跳响应
        if (message.type === 'pong') {
          console.log('Received heartbeat pong');
          return;
        }

        setMessages((prev) => [...prev, message]);
      } catch (error) {
        console.error('Error parsing message:', error);
      }
    };

    ws.onclose = (event) => {
      setConnected(false);
      console.log('WebSocket disconnected at', new Date().toLocaleTimeString(), 'Code:', event.code, 'Reason:', event.reason);

      // 清理心跳定时器
      if (heartbeatRef.current) {
        clearInterval(heartbeatRef.current);
        heartbeatRef.current = null;
      }

      // 暂时禁用自动重连，避免多连接问题
      console.log('WebSocket disconnected, auto-reconnect disabled for debugging');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error at', new Date().toLocaleTimeString(), error);
    };

    wsRef.current = ws;
  };

  const handleSendMessage = () => {
    console.log('handleSendMessage called', {
      inputMessage: inputMessage.trim(),
      hasWs: !!wsRef.current,
      connected,
      wsReadyState: wsRef.current?.readyState
    });

    if (inputMessage.trim() && wsRef.current && connected) {
      console.log('Sending message via WebSocket...');
      sendMessage(wsRef.current, 'chat', inputMessage.trim());
      setInputMessage('');
    } else {
      console.log('Cannot send message:', {
        hasMessage: !!inputMessage.trim(),
        hasWs: !!wsRef.current,
        connected,
        wsReadyState: wsRef.current?.readyState
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const renderMessage = (message: Message) => {
    const isSystemMessage = ['join', 'leave', 'system'].includes(message.type);
    
    if (message.type === 'gift') {
      return (
        <Paper
          key={message.id}
          sx={{
            p: 1,
            mb: 1,
            backgroundColor: 'rgba(255, 215, 0, 0.1)',
            border: '1px solid gold',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
            <Avatar src={message.avatar} sx={{ width: 20, height: 20, mr: 1 }} />
            <Typography variant="body2" sx={{ fontWeight: 'bold', color: 'gold' }}>
              {message.nickname}
            </Typography>
            <Chip label={`Lv.${message.level}`} size="small" sx={{ ml: 1, height: 16 }} />
          </Box>
          <Typography variant="body2">
            送出了 {message.extra?.gift_name} x{message.extra?.count}
          </Typography>
          {message.content && (
            <Typography variant="body2" sx={{ fontStyle: 'italic', mt: 0.5 }}>
              "{message.content}"
            </Typography>
          )}
        </Paper>
      );
    }

    return (
      <ListItem key={message.id} sx={{ py: 0.5, px: 1 }}>
        <Box sx={{ width: '100%' }}>
          {isSystemMessage ? (
            <Typography
              variant="body2"
              sx={{
                color: 'text.secondary',
                fontStyle: 'italic',
                textAlign: 'center',
              }}
            >
              {message.content}
            </Typography>
          ) : (
            <>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                <Avatar src={message.avatar} sx={{ width: 20, height: 20, mr: 1 }} />
                <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                  {message.nickname}
                </Typography>
                <Chip label={`Lv.${message.level}`} size="small" sx={{ ml: 1, height: 16 }} />
              </Box>
              <Typography variant="body2" sx={{ wordBreak: 'break-word' }}>
                {message.content}
              </Typography>
            </>
          )}
        </Box>
      </ListItem>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 聊天消息区域 */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', maxHeight: '400px' }}>
        <List sx={{ py: 0 }}>
          {messages.map(renderMessage)}
          <div ref={messagesEndRef} />
        </List>
      </Box>

      {/* 输入区域 */}
      <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <TextField
            fullWidth
            size="small"
            placeholder={connected ? '说点什么...' : '连接中...'}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={!connected}
            multiline
            maxRows={3}
          />
          <Button
            variant="contained"
            size="small"
            onClick={handleSendMessage}
            disabled={!connected || !inputMessage.trim()}
            sx={{ minWidth: 'auto', px: 1 }}
          >
            <Send fontSize="small" />
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ChatRoom;
