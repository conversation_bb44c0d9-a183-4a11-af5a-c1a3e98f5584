import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8081';

// 创建axios实例
const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  timeout: 10000,
});

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      // 重定向到登录页面
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const login = (username: string, password: string) => {
  return api.post('/auth/login', { username, password });
};

export const register = (username: string, email: string, password: string) => {
  return api.post('/auth/register', { username, email, password });
};

// 用户相关API
export const getUserProfile = () => {
  return api.get('/user/profile');
};

export const updateProfile = (data: { nickname?: string; avatar?: string }) => {
  return api.put('/user/profile', data);
};

// 房间相关API
export const getRooms = (page = 1, limit = 20) => {
  return api.get('/room/list', { params: { page, limit } });
};

export const getRoom = (id: number) => {
  return api.get(`/room/${id}`);
};

export const createRoom = (data: { title: string; description?: string }) => {
  return api.post('/room/create', data);
};

export const updateRoom = (id: number, data: any) => {
  return api.put(`/room/${id}`, data);
};

export const startLive = (id: number) => {
  return api.post(`/room/${id}/start`);
};

export const stopLive = (id: number) => {
  return api.post(`/room/${id}/stop`);
};

export const incrementView = (id: number) => {
  return api.post(`/room/${id}/view`);
};

export const getOnlineViewers = (id: number) => {
  return api.get(`/room/${id}/viewers`);
};

export const getStreamInfo = (id: number) => {
  return api.get(`/room/${id}/stream`);
};

// 礼物相关API
export const getGifts = () => {
  return api.get('/gift/list');
};

export const sendGift = (data: {
  room_id: number;
  gift_id: number;
  count: number;
  message?: string;
}) => {
  return api.post('/gift/send', data);
};

export const getRoomGiftRecords = (roomId: number, limit = 50) => {
  return api.get(`/room/${roomId}/gifts`, { params: { limit } });
};

export const getUserGiftHistory = (page = 1, limit = 20) => {
  return api.get('/user/gifts', { params: { page, limit } });
};

export default api;
