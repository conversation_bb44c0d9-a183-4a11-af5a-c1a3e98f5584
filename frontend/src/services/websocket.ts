const WEBSOCKET_URL = process.env.REACT_APP_CHATROOM_URL || 'ws://localhost:8082';

export const connectWebSocket = (
  roomId: string,
  userId: string,
  username: string,
  nickname: string,
  avatar: string
): WebSocket => {
  const params = new URLSearchParams({
    room_id: roomId,
    user_id: userId,
    username: username,
    nickname: nickname,
    avatar: avatar,
  });

  const url = `${WEBSOCKET_URL}/ws?${params.toString()}`;
  console.log('Connecting to WebSocket:', url);

  const ws = new WebSocket(url);

  // 添加连接状态监控
  ws.addEventListener('open', () => {
    console.log('WebSocket connection opened');
  });

  ws.addEventListener('close', (event) => {
    console.log('WebSocket connection closed:', event.code, event.reason);
  });

  ws.addEventListener('error', (error) => {
    console.error('WebSocket connection error:', error);
  });

  return ws;
};

export const sendMessage = (
  ws: WebSocket,
  type: 'chat' | 'gift',
  content: string,
  extra?: any
) => {
  if (ws.readyState === WebSocket.OPEN) {
    // 从localStorage获取用户信息
    const userStr = localStorage.getItem('user');
    const user = userStr ? JSON.parse(userStr) : null;

    const message = {
      type,
      content,
      user_id: user?.id?.toString() || '',
      username: user?.username || '',
      nickname: user?.nickname || user?.username || '',
      avatar: user?.avatar || '',
      extra,
    };
    console.log('Sending message:', message);
    ws.send(JSON.stringify(message));
  } else {
    console.log('WebSocket not ready, readyState:', ws.readyState);
  }
};

export const sendGiftMessage = (
  ws: WebSocket,
  giftData: {
    gift_id: string;
    gift_name: string;
    gift_icon: string;
    gift_price: number;
    count: number;
    effect: string;
  },
  message?: string
) => {
  sendMessage(ws, 'gift', message || '', giftData);
};
