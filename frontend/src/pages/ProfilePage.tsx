import React, { useState, useEffect } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  Avatar,
  Button,
  TextField,
  Grid,
  Alert,
  Card,
  CardContent,
  Stack,
  Chip,
  LinearProgress,
  Divider,
  AppBar,
  Toolbar,
  IconButton,
} from '@mui/material';
import {
  Edit,
  Save,
  Cancel,
  AccountCircle,
  MonetizationOn,
  TrendingUp,
  Star,
  EmojiEvents,
  ArrowBack,
  PhotoCamera,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getUserProfile, updateProfile } from '../services/api';

interface User {
  id: number;
  username: string;
  nickname: string;
  email: string;
  avatar: string;
  level: number;
  coins: number;
  is_streamer: boolean;
  created_at: string;
}

const ProfilePage: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [editing, setEditing] = useState(false);
  const [formData, setFormData] = useState({
    nickname: '',
    avatar: '',
  });
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const response = await getUserProfile();
      setUser(response.data.user);
      setFormData({
        nickname: response.data.user.nickname || '',
        avatar: response.data.user.avatar || '',
      });
    } catch (error) {
      console.error('Failed to load profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await updateProfile(formData);
      setMessage('个人信息更新成功');
      setEditing(false);
      loadProfile();
    } catch (error: any) {
      setMessage(error.response?.data?.error || '更新失败');
    }
  };

  if (loading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="h6" color="white">
          加载中...
        </Typography>
      </Box>
    );
  }

  if (!user) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            请先登录
          </Typography>
          <Button variant="contained" onClick={() => navigate('/login')}>
            去登录
          </Button>
        </Paper>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
      }}
    >
      {/* 顶部导航栏 */}
      <AppBar position="static" elevation={0}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            个人中心
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 6 }}>
        {message && (
          <Alert
            severity="success"
            sx={{
              mb: 4,
              borderRadius: 2,
              '& .MuiAlert-message': {
                fontSize: '1rem',
              },
            }}
          >
            {message}
          </Alert>
        )}

        <Grid container spacing={4}>
          {/* 左侧用户信息卡片 */}
          <Grid item xs={12} lg={4}>
            <Card
              sx={{
                background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                color: 'white',
                textAlign: 'center',
                position: 'relative',
                overflow: 'visible',
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
                  <Avatar
                    src={user.avatar}
                    sx={{
                      width: 120,
                      height: 120,
                      border: '4px solid rgba(255,255,255,0.3)',
                      boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                    }}
                  >
                    <AccountCircle sx={{ fontSize: 80 }} />
                  </Avatar>
                  {editing && (
                    <IconButton
                      sx={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        backgroundColor: 'rgba(255,255,255,0.9)',
                        color: '#6366f1',
                        '&:hover': {
                          backgroundColor: 'white',
                        },
                      }}
                    >
                      <PhotoCamera />
                    </IconButton>
                  )}
                </Box>

                <Typography variant="h4" fontWeight={700} gutterBottom>
                  {user.nickname || user.username}
                </Typography>

                <Stack direction="row" spacing={1} justifyContent="center" sx={{ mb: 3 }}>
                  <Chip
                    icon={<Star />}
                    label={`等级 ${user.level}`}
                    sx={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      color: 'white',
                      fontWeight: 600,
                    }}
                  />
                  {user.is_streamer && (
                    <Chip
                      icon={<EmojiEvents />}
                      label="主播"
                      sx={{
                        backgroundColor: '#fbbf24',
                        color: 'white',
                        fontWeight: 600,
                      }}
                    />
                  )}
                </Stack>

                <Box
                  sx={{
                    backgroundColor: 'rgba(255,255,255,0.1)',
                    borderRadius: 2,
                    p: 2,
                    mb: 2,
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="center" spacing={1}>
                    <MonetizationOn sx={{ color: '#fbbf24' }} />
                    <Typography variant="h5" fontWeight={700}>
                      {user.coins}
                    </Typography>
                    <Typography variant="body1">
                      金币
                    </Typography>
                  </Stack>
                </Box>

                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  注册于 {new Date(user.created_at).toLocaleDateString()}
                </Typography>
              </CardContent>
            </Card>

            {/* 统计卡片 */}
            <Grid container spacing={2} sx={{ mt: 2 }}>
              <Grid item xs={6}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <TrendingUp sx={{ fontSize: 32, color: '#10b981', mb: 1 }} />
                  <Typography variant="h6" fontWeight={700}>
                    0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    直播时长
                  </Typography>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card sx={{ textAlign: 'center', p: 2 }}>
                  <EmojiEvents sx={{ fontSize: 32, color: '#f59e0b', mb: 1 }} />
                  <Typography variant="h6" fontWeight={700}>
                    0
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    获得礼物
                  </Typography>
                </Card>
              </Grid>
            </Grid>
          </Grid>

          {/* 右侧详细信息 */}
          <Grid item xs={12} lg={8}>
            <Card sx={{ mb: 3 }}>
              <CardContent sx={{ p: 4 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h5" fontWeight={700}>
                    基本信息
                  </Typography>
                  {!editing && (
                    <Button
                      variant="contained"
                      startIcon={<Edit />}
                      onClick={() => setEditing(true)}
                      sx={{
                        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                      }}
                    >
                      编辑资料
                    </Button>
                  )}
                </Box>

                <Divider sx={{ mb: 3 }} />

                {editing ? (
                  <Box component="form" onSubmit={handleSubmit}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="昵称"
                          name="nickname"
                          value={formData.nickname}
                          onChange={handleChange}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                            },
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="头像URL"
                          name="avatar"
                          value={formData.avatar}
                          onChange={handleChange}
                          variant="outlined"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                            },
                          }}
                        />
                      </Grid>
                    </Grid>

                    <Stack direction="row" spacing={2} sx={{ mt: 4 }}>
                      <Button
                        type="submit"
                        variant="contained"
                        startIcon={<Save />}
                        sx={{
                          background: 'linear-gradient(135deg, #10b981 0%, #34d399 100%)',
                          px: 3,
                        }}
                      >
                        保存更改
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<Cancel />}
                        onClick={() => setEditing(false)}
                        sx={{ px: 3 }}
                      >
                        取消
                      </Button>
                    </Stack>
                  </Box>
                ) : (
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          用户名
                        </Typography>
                        <Typography variant="h6" fontWeight={600}>
                          {user.username}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          邮箱地址
                        </Typography>
                        <Typography variant="h6" fontWeight={600}>
                          {user.email}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          显示昵称
                        </Typography>
                        <Typography variant="h6" fontWeight={600}>
                          {user.nickname || '未设置'}
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          账户类型
                        </Typography>
                        <Typography variant="h6" fontWeight={600}>
                          {user.is_streamer ? '主播账户' : '普通用户'}
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>
                )}
              </CardContent>
            </Card>

            {/* 等级进度卡片 */}
            <Card>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" fontWeight={700} gutterBottom>
                  等级进度
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Box sx={{ mb: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1" fontWeight={600}>
                      当前等级: {user.level}
                    </Typography>
                    <Typography variant="body1" fontWeight={600}>
                      下一等级: {user.level + 1}
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={75}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      backgroundColor: '#e2e8f0',
                      '& .MuiLinearProgress-bar': {
                        background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                        borderRadius: 4,
                      },
                    }}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    还需要 250 经验值升级
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default ProfilePage;
