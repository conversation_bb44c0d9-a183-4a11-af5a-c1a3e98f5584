import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Container,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar,
  Button,
  AppBar,
  Toolbar,
  IconButton,
  Paper,
  Stack,
  Tabs,
  Tab,
  Divider,
} from '@mui/material';
import {
  ArrowBack,
  PlayArrow,
  Visibility,
  Person,
  LiveTv,
  TrendingUp,
  Gamepad,
  MusicNote,
  SportsEsports,
  School,
  Palette,
  Restaurant,
  FitnessCenter,
  Pets,
} from '@mui/icons-material';
import { getRooms } from '../services/api';

interface Room {
  id: number;
  title: string;
  description: string;
  cover: string;
  is_live: boolean;
  view_count: number;
  category: string;
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar: string;
  };
}

interface Category {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  description: string;
  count: number;
}

const categories: Category[] = [
  {
    id: 'gaming',
    name: '游戏',
    icon: <SportsEsports />,
    color: '#6366f1',
    description: '热门游戏直播',
    count: 1250,
  },
  {
    id: 'music',
    name: '音乐',
    icon: <MusicNote />,
    color: '#ec4899',
    description: '音乐表演与创作',
    count: 680,
  },
  {
    id: 'education',
    name: '教育',
    icon: <School />,
    color: '#10b981',
    description: '知识分享与学习',
    count: 420,
  },
  {
    id: 'art',
    name: '艺术',
    icon: <Palette />,
    color: '#f59e0b',
    description: '绘画与创意设计',
    count: 350,
  },
  {
    id: 'food',
    name: '美食',
    icon: <Restaurant />,
    color: '#ef4444',
    description: '烹饪与美食分享',
    count: 280,
  },
  {
    id: 'fitness',
    name: '健身',
    icon: <FitnessCenter />,
    color: '#8b5cf6',
    description: '运动健身指导',
    count: 190,
  },
  {
    id: 'pets',
    name: '萌宠',
    icon: <Pets />,
    color: '#06b6d4',
    description: '可爱宠物展示',
    count: 150,
  },
  {
    id: 'other',
    name: '其他',
    icon: <LiveTv />,
    color: '#64748b',
    description: '其他精彩内容',
    count: 320,
  },
];

const ChannelPage: React.FC = () => {
  const { category } = useParams<{ category: string }>();
  const navigate = useNavigate();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);

  const currentCategory = categories.find(cat => cat.id === category) || categories[0];

  useEffect(() => {
    loadRooms();
  }, [category]);

  const loadRooms = async () => {
    try {
      const response = await getRooms();
      // 根据分类过滤房间
      const filteredRooms = response.data.rooms?.filter((room: Room) => 
        category === 'all' || room.category === category || 
        (category === 'other' && !categories.some(cat => cat.id === room.category))
      ) || [];
      setRooms(filteredRooms);
    } catch (error) {
      console.error('Failed to load rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoomClick = (roomId: number) => {
    navigate(`/room/${roomId}`);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
      }}
    >
      {/* 顶部导航栏 */}
      <AppBar position="static" elevation={0}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            <ArrowBack />
          </IconButton>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 40,
                height: 40,
                borderRadius: 2,
                backgroundColor: 'rgba(255,255,255,0.2)',
                mr: 2,
                color: currentCategory.color,
              }}
            >
              {currentCategory.icon}
            </Box>
            <Box>
              <Typography variant="h6" component="div" fontWeight={700}>
                {currentCategory.name}频道
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                {currentCategory.description}
              </Typography>
            </Box>
          </Box>
          <Chip
            label={`${currentCategory.count} 个直播间`}
            sx={{
              backgroundColor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 600,
            }}
          />
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* 分类导航 */}
        <Paper sx={{ mb: 4, borderRadius: 3 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              '& .MuiTab-root': {
                minHeight: 72,
                textTransform: 'none',
                fontWeight: 600,
              },
            }}
          >
            <Tab
              label={
                <Stack direction="row" spacing={1} alignItems="center">
                  <LiveTv />
                  <Typography>全部</Typography>
                </Stack>
              }
            />
            <Tab
              label={
                <Stack direction="row" spacing={1} alignItems="center">
                  <TrendingUp />
                  <Typography>热门</Typography>
                </Stack>
              }
            />
            <Tab
              label={
                <Stack direction="row" spacing={1} alignItems="center">
                  <PlayArrow />
                  <Typography>最新</Typography>
                </Stack>
              }
            />
          </Tabs>
        </Paper>

        {/* 频道统计 */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              sx={{
                p: 3,
                textAlign: 'center',
                background: `linear-gradient(135deg, ${currentCategory.color} 0%, ${currentCategory.color}CC 100%)`,
                color: 'white',
              }}
            >
              {currentCategory.icon}
              <Typography variant="h4" fontWeight={700} sx={{ mt: 1 }}>
                {rooms.filter(room => room.is_live).length}
              </Typography>
              <Typography variant="body1">正在直播</Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Visibility sx={{ fontSize: 32, color: '#10b981', mb: 1 }} />
              <Typography variant="h4" fontWeight={700}>
                {rooms.reduce((sum, room) => sum + room.view_count, 0)}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                总观看数
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Person sx={{ fontSize: 32, color: '#f59e0b', mb: 1 }} />
              <Typography variant="h4" fontWeight={700}>
                {currentCategory.count}
              </Typography>
              <Typography variant="body1" color="text.secondary">
                主播数量
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <TrendingUp sx={{ fontSize: 32, color: '#ec4899', mb: 1 }} />
              <Typography variant="h4" fontWeight={700}>
                98%
              </Typography>
              <Typography variant="body1" color="text.secondary">
                满意度
              </Typography>
            </Paper>
          </Grid>
        </Grid>

        {/* 直播间列表 */}
        {loading ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h6" color="text.secondary">
              加载中...
            </Typography>
          </Box>
        ) : (
          <Grid container spacing={3}>
            {rooms.map((room) => (
              <Grid item xs={12} sm={6} lg={4} key={room.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    overflow: 'hidden',
                  }}
                  onClick={() => handleRoomClick(room.id)}
                >
                  {/* 直播封面 */}
                  <Box sx={{ position: 'relative' }}>
                    <CardMedia
                      component="img"
                      height="200"
                      image={room.cover || `https://via.placeholder.com/400x200/${currentCategory.color.slice(1)}/ffffff?text=${currentCategory.name}`}
                      alt={room.title}
                      sx={{
                        backgroundColor: currentCategory.color,
                        objectFit: 'cover',
                      }}
                    />

                    {/* 直播状态标签 */}
                    {room.is_live && (
                      <Chip
                        icon={<PlayArrow />}
                        label="直播中"
                        size="small"
                        sx={{
                          position: 'absolute',
                          top: 12,
                          left: 12,
                          backgroundColor: '#ef4444',
                          color: 'white',
                          fontWeight: 600,
                          '& .MuiChip-icon': { color: 'white' },
                        }}
                      />
                    )}

                    {/* 观看数 */}
                    <Paper
                      sx={{
                        position: 'absolute',
                        bottom: 12,
                        right: 12,
                        px: 1.5,
                        py: 0.5,
                        backgroundColor: 'rgba(0,0,0,0.7)',
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                      }}
                    >
                      <Visibility sx={{ fontSize: 16 }} />
                      <Typography variant="body2" fontWeight={600}>
                        {room.view_count}
                      </Typography>
                    </Paper>
                  </Box>

                  {/* 卡片内容 */}
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    <Typography
                      variant="h6"
                      component="h2"
                      sx={{
                        mb: 1,
                        fontWeight: 600,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {room.title}
                    </Typography>

                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        mb: 2,
                        height: '40px',
                        overflow: 'hidden',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {room.description || '暂无描述'}
                    </Typography>

                    {/* 主播信息 */}
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Avatar
                        src={room.user.avatar}
                        sx={{ width: 32, height: 32 }}
                      >
                        <Person />
                      </Avatar>
                      <Typography variant="body2" fontWeight={500}>
                        {room.user.nickname || room.user.username}
                      </Typography>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* 空状态 */}
        {!loading && rooms.length === 0 && (
          <Paper
            sx={{
              textAlign: 'center',
              py: 8,
              px: 4,
              background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            }}
          >
            {currentCategory.icon}
            <Typography variant="h5" color="text.secondary" sx={{ mb: 1, mt: 2 }}>
              暂无{currentCategory.name}直播
            </Typography>
            <Typography variant="body1" color="text.secondary">
              成为第一个在{currentCategory.name}频道开播的主播吧！
            </Typography>
          </Paper>
        )}
      </Container>
    </Box>
  );
};

export default ChannelPage;
