import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Button,
  TextField,
  Grid,
  <PERSON>ert,
  Card,
  CardContent,
  CardActions,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  AppBar,
  <PERSON>lbar,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Add,
  PlayArrow,
  Stop,
  Edit,
  Visibility,
  ArrowBack,
  LiveTv,
  Key,
  ContentCopy,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getRooms, createRoom, startLive, stopLive, getStreamInfo } from '../services/api';

interface Room {
  id: number;
  title: string;
  description: string;
  stream_key: string;
  stream_url?: string;
  push_url?: string;
  is_live: boolean;
  view_count: number;
  category: string;
  tags: string;
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar?: string;
  };
  created_at: string;
}

interface StreamInfo {
  room_id: number;
  is_live: boolean;
  stream_url?: string;
  push_url?: string;
  stream_key?: string;
}

const RoomManagePage: React.FC = () => {
  const navigate = useNavigate();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [streamKeyDialogOpen, setStreamKeyDialogOpen] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [newRoom, setNewRoom] = useState({
    title: '',
    description: '',
  });



  useEffect(() => {
    loadRooms();
  }, []);

  const loadRooms = async () => {
    try {
      setLoading(true);
      setError('');

      // 获取直播间列表 - 这里使用getRooms API
      // 注意：getRooms返回的是直播中的房间，你可能需要一个获取用户房间的API
      const response = await getRooms(1, 20);
      const roomsData = response.data.rooms || [];

      // 为每个房间获取流信息
      const roomsWithStreamInfo = await Promise.all(
        roomsData.map(async (room: Room) => {
          try {
            const streamResponse = await getStreamInfo(room.id);
            return {
              ...room,
              ...streamResponse.data.stream_info
            };
          } catch (err) {
            console.error(`Failed to get stream info for room ${room.id}:`, err);
            return room;
          }
        })
      );

      setRooms(roomsWithStreamInfo);
    } catch (err: any) {
      console.error('Failed to load rooms:', err);
      setError('加载直播间失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRoom = async () => {
    try {
      if (!newRoom.title.trim()) {
        setError('请输入直播间标题');
        return;
      }

      await createRoom({
        title: newRoom.title,
        description: newRoom.description
      });

      setCreateDialogOpen(false);
      setNewRoom({ title: '', description: '' });
      setError('');
      loadRooms();
    } catch (err: any) {
      console.error('Failed to create room:', err);
      setError(err.response?.data?.error || '创建直播间失败');
    }
  };

  const handleStartLive = async (roomId: number) => {
    try {
      await startLive(roomId);
      setError('');
      loadRooms();
    } catch (err: any) {
      console.error('Failed to start live:', err);
      setError(err.response?.data?.error || '开始直播失败');
    }
  };

  const handleStopLive = async (roomId: number) => {
    try {
      await stopLive(roomId);
      setError('');
      loadRooms();
    } catch (err: any) {
      console.error('Failed to stop live:', err);
      setError(err.response?.data?.error || '停止直播失败');
    }
  };

  const copyStreamKey = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // 这里可以添加复制成功的提示，比如使用snackbar
      console.log('复制成功:', text);
    }).catch(err => {
      console.error('复制失败:', err);
    });
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            直播间管理
          </Typography>
          <Button
            color="inherit"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
          >
            创建直播间
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Typography>加载中...</Typography>
        ) : (
          <Grid container spacing={3}>
            {rooms.map((room) => (
              <Grid item xs={12} md={6} lg={4} key={room.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <LiveTv color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        {room.title}
                      </Typography>
                      <Box ml="auto">
                        <Chip
                          label={room.is_live ? '直播中' : '未开播'}
                          color={room.is_live ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {room.description}
                    </Typography>

                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                      <Box display="flex" alignItems="center">
                        <Visibility fontSize="small" sx={{ mr: 0.5 }} />
                        <Typography variant="body2">{room.view_count}</Typography>
                      </Box>
                      <Chip label={room.category} size="small" variant="outlined" />
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="body2" color="text.secondary">
                      主播: {room.user.nickname}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      创建时间: {new Date(room.created_at).toLocaleDateString()}
                    </Typography>
                  </CardContent>

                  <CardActions>
                    {room.is_live ? (
                      <Button
                        size="small"
                        startIcon={<Stop />}
                        onClick={() => handleStopLive(room.id)}
                        color="error"
                      >
                        停止直播
                      </Button>
                    ) : (
                      <Button
                        size="small"
                        startIcon={<PlayArrow />}
                        onClick={() => handleStartLive(room.id)}
                        color="success"
                      >
                        开始直播
                      </Button>
                    )}
                    <Button
                      size="small"
                      startIcon={<Key />}
                      onClick={() => {
                        setSelectedRoom(room);
                        setStreamKeyDialogOpen(true);
                      }}
                    >
                      推流密钥
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Edit />}
                      onClick={() => navigate(`/room/${room.id}/edit`)}
                    >
                      编辑
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* 创建直播间对话框 */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>创建新直播间</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="直播间标题"
              fullWidth
              variant="outlined"
              value={newRoom.title}
              onChange={(e) => setNewRoom({ ...newRoom, title: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="dense"
              label="直播间描述"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={newRoom.description}
              onChange={(e) => setNewRoom({ ...newRoom, description: e.target.value })}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
            <Button onClick={handleCreateRoom} variant="contained">创建</Button>
          </DialogActions>
        </Dialog>

        {/* 推流密钥对话框 */}
        <Dialog open={streamKeyDialogOpen} onClose={() => setStreamKeyDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>直播配置信息</DialogTitle>
          <DialogContent>
            <Alert severity="warning" sx={{ mb: 3 }}>
              请妥善保管您的推流信息，不要泄露给他人
            </Alert>

            {/* 推流地址 */}
            {selectedRoom?.push_url && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                  推流地址 (RTMP URL)
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    value={selectedRoom.push_url}
                    InputProps={{
                      readOnly: true,
                      style: { fontFamily: 'monospace', fontSize: '0.875rem' }
                    }}
                  />
                  <Button
                    variant="outlined"
                    startIcon={<ContentCopy />}
                    onClick={() => selectedRoom && copyStreamKey(selectedRoom.push_url || '')}
                  >
                    复制
                  </Button>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  在OBS中设置为"服务器"地址
                </Typography>
              </Box>
            )}

            {/* 推流密钥 */}
            {selectedRoom?.stream_key && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                  推流密钥 (Stream Key)
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    value={selectedRoom.stream_key}
                    InputProps={{
                      readOnly: true,
                      style: { fontFamily: 'monospace', fontSize: '0.875rem' }
                    }}
                  />
                  <Button
                    variant="outlined"
                    startIcon={<ContentCopy />}
                    onClick={() => selectedRoom && copyStreamKey(selectedRoom.stream_key)}
                  >
                    复制
                  </Button>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  在OBS中设置为"串流密钥"
                </Typography>
              </Box>
            )}

            {/* 播放地址 */}
            {selectedRoom?.stream_url && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                  播放地址 (HLS URL)
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <TextField
                    fullWidth
                    variant="outlined"
                    value={selectedRoom.stream_url}
                    InputProps={{
                      readOnly: true,
                      style: { fontFamily: 'monospace', fontSize: '0.875rem' }
                    }}
                  />
                  <Button
                    variant="outlined"
                    startIcon={<ContentCopy />}
                    onClick={() => selectedRoom && copyStreamKey(selectedRoom.stream_url || '')}
                  >
                    复制
                  </Button>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  观众观看直播的地址
                </Typography>
              </Box>
            )}

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>OBS设置步骤：</strong><br/>
                1. 打开OBS Studio<br/>
                2. 点击"设置" → "推流"<br/>
                3. 服务选择"自定义"<br/>
                4. 服务器填入推流地址<br/>
                5. 串流密钥填入推流密钥
              </Typography>
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStreamKeyDialogOpen(false)}>关闭</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default RoomManagePage;
