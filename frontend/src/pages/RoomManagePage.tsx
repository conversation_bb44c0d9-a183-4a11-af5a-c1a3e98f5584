import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  TextField,
  Grid,
  Alert,
  Card,
  CardContent,
  CardActions,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  AppBar,
  <PERSON>lbar,
  IconButton,
  Divider,
} from '@mui/material';
import {
  Add,
  PlayArrow,
  Stop,
  Edit,
  Visibility,
  ArrowBack,
  LiveTv,
  Key,
  ContentCopy,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface Room {
  id: number;
  title: string;
  description: string;
  stream_key: string;
  is_live: boolean;
  view_count: number;
  category: string;
  tags: string;
  user: {
    username: string;
    nickname: string;
  };
  created_at: string;
}

const RoomManagePage: React.FC = () => {
  const navigate = useNavigate();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [streamKeyDialogOpen, setStreamKeyDialogOpen] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [newRoom, setNewRoom] = useState({
    title: '',
    description: '',
  });

  // 模拟数据 - 在实际应用中应该从API获取
  const mockRoom: Room = {
    id: 1,
    title: '我的精彩直播间',
    description: '欢迎来到我的直播间！这里有最精彩的内容等着你。快来关注我吧！',
    stream_key: '7136eabeeef5a624d15dfe93a25acaee',
    is_live: true,
    view_count: 0,
    category: '娱乐',
    tags: '游戏,聊天,音乐',
    user: {
      username: 'demo_streamer',
      nickname: '演示主播',
    },
    created_at: new Date().toISOString(),
  };

  useEffect(() => {
    loadRooms();
  }, []);

  const loadRooms = async () => {
    try {
      setLoading(true);
      // 模拟API调用
      setTimeout(() => {
        setRooms([mockRoom]);
        setLoading(false);
      }, 1000);
    } catch (err) {
      setError('加载直播间失败');
      setLoading(false);
    }
  };

  const handleCreateRoom = async () => {
    try {
      // 这里应该调用API创建直播间
      console.log('创建直播间:', newRoom);
      setCreateDialogOpen(false);
      setNewRoom({ title: '', description: '' });
      loadRooms();
    } catch (err) {
      setError('创建直播间失败');
    }
  };

  const handleStartLive = async (roomId: number) => {
    try {
      // 这里应该调用API开始直播
      console.log('开始直播:', roomId);
      loadRooms();
    } catch (err) {
      setError('开始直播失败');
    }
  };

  const handleStopLive = async (roomId: number) => {
    try {
      // 这里应该调用API停止直播
      console.log('停止直播:', roomId);
      loadRooms();
    } catch (err) {
      setError('停止直播失败');
    }
  };

  const copyStreamKey = (streamKey: string) => {
    navigator.clipboard.writeText(streamKey);
    // 这里可以添加复制成功的提示
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static">
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={() => navigate('/')}
            sx={{ mr: 2 }}
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            直播间管理
          </Typography>
          <Button
            color="inherit"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
          >
            创建直播间
          </Button>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Typography>加载中...</Typography>
        ) : (
          <Grid container spacing={3}>
            {rooms.map((room) => (
              <Grid item xs={12} md={6} lg={4} key={room.id}>
                <Card>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                      <LiveTv color="primary" sx={{ mr: 1 }} />
                      <Typography variant="h6" component="div">
                        {room.title}
                      </Typography>
                      <Box ml="auto">
                        <Chip
                          label={room.is_live ? '直播中' : '未开播'}
                          color={room.is_live ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {room.description}
                    </Typography>

                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                      <Box display="flex" alignItems="center">
                        <Visibility fontSize="small" sx={{ mr: 0.5 }} />
                        <Typography variant="body2">{room.view_count}</Typography>
                      </Box>
                      <Chip label={room.category} size="small" variant="outlined" />
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="body2" color="text.secondary">
                      主播: {room.user.nickname}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      创建时间: {new Date(room.created_at).toLocaleDateString()}
                    </Typography>
                  </CardContent>

                  <CardActions>
                    {room.is_live ? (
                      <Button
                        size="small"
                        startIcon={<Stop />}
                        onClick={() => handleStopLive(room.id)}
                        color="error"
                      >
                        停止直播
                      </Button>
                    ) : (
                      <Button
                        size="small"
                        startIcon={<PlayArrow />}
                        onClick={() => handleStartLive(room.id)}
                        color="success"
                      >
                        开始直播
                      </Button>
                    )}
                    <Button
                      size="small"
                      startIcon={<Key />}
                      onClick={() => {
                        setSelectedRoom(room);
                        setStreamKeyDialogOpen(true);
                      }}
                    >
                      推流密钥
                    </Button>
                    <Button
                      size="small"
                      startIcon={<Edit />}
                      onClick={() => navigate(`/room/${room.id}/edit`)}
                    >
                      编辑
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* 创建直播间对话框 */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>创建新直播间</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              label="直播间标题"
              fullWidth
              variant="outlined"
              value={newRoom.title}
              onChange={(e) => setNewRoom({ ...newRoom, title: e.target.value })}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="dense"
              label="直播间描述"
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              value={newRoom.description}
              onChange={(e) => setNewRoom({ ...newRoom, description: e.target.value })}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>取消</Button>
            <Button onClick={handleCreateRoom} variant="contained">创建</Button>
          </DialogActions>
        </Dialog>

        {/* 推流密钥对话框 */}
        <Dialog open={streamKeyDialogOpen} onClose={() => setStreamKeyDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>推流密钥</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              请妥善保管您的推流密钥，不要泄露给他人
            </Alert>
            <Box display="flex" alignItems="center" gap={1}>
              <TextField
                fullWidth
                variant="outlined"
                value={selectedRoom?.stream_key || ''}
                InputProps={{
                  readOnly: true,
                }}
              />
              <Button
                variant="outlined"
                startIcon={<ContentCopy />}
                onClick={() => selectedRoom && copyStreamKey(selectedRoom.stream_key)}
              >
                复制
              </Button>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              使用OBS等推流软件时，请将此密钥设置为流密钥
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStreamKeyDialogOpen(false)}>关闭</Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  );
};

export default RoomManagePage;
