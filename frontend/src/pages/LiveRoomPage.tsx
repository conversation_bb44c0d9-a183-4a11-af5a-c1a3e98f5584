import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Avatar,
  Chip,
} from '@mui/material';
import VideoPlayer from '../components/VideoPlayer';
import ChatRoom from '../components/ChatRoom';
import GiftPanel from '../components/GiftPanel';
import { getRoom } from '../services/api';

interface Room {
  id: number;
  title: string;
  description: string;
  is_live: boolean;
  view_count: number;
  category: string;
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar: string;
  };
}

const LiveRoomPage: React.FC = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const [room, setRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);

  useEffect(() => {
    if (roomId) {
      loadRoom(parseInt(roomId));
    }
  }, [roomId]);

  const loadRoom = async (id: number) => {
    try {
      const response = await getRoom(id);
      setRoom(response.data.room);
    } catch (error) {
      console.error('Failed to load room:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <Typography>加载中...</Typography>
      </Container>
    );
  }

  if (!room) {
    return (
      <Container>
        <Typography>房间不存在</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 2 }}>
      <Grid container spacing={2}>
        {/* 左侧视频区域 */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 0, mb: 2 }}>
            <VideoPlayer roomId={room.id} isLive={room.is_live} />
          </Paper>
          
          {/* 房间信息 */}
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                src={room.user.avatar}
                sx={{ width: 48, height: 48, mr: 2 }}
              />
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h5" component="h1">
                  {room.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {room.user.nickname || room.user.username}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  label={room.category}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
                {room.is_live && (
                  <Chip
                    label="直播中"
                    size="small"
                    color="error"
                  />
                )}
                <Typography variant="body2" color="text.secondary">
                  观看: {room.view_count}
                </Typography>
              </Box>
            </Box>
            
            {room.description && (
              <Typography variant="body1" color="text.secondary">
                {room.description}
              </Typography>
            )}
          </Paper>
        </Grid>

        {/* 右侧聊天和礼物区域 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
            {/* 聊天室 */}
            <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <ChatRoom
                roomId={room.id}
                onWebSocketReady={(ws) => setWebsocket(ws)}
              />
            </Box>

            {/* 礼物面板 */}
            <Box sx={{ borderTop: 1, borderColor: 'divider' }}>
              <GiftPanel roomId={room.id} websocket={websocket || undefined} />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default LiveRoomPage;
