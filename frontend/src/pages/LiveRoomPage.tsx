import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Avatar,
  Chip,
  Alert,
  Button,
  Collapse,
  IconButton,
} from '@mui/material';
import { ContentCopy, Visibility, VisibilityOff } from '@mui/icons-material';
import VideoPlayer from '../components/VideoPlayer';
import ChatRoom from '../components/ChatRoom';
import GiftPanel from '../components/GiftPanel';
import { getRoom, getStreamInfo } from '../services/api';

interface Room {
  id: number;
  title: string;
  description: string;
  is_live: boolean;
  view_count: number;
  category: string;
  stream_url?: string;
  push_url?: string;
  stream_key?: string;
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar: string;
  };
}

interface StreamInfo {
  room_id: number;
  is_live: boolean;
  stream_url?: string;
  push_url?: string;
  stream_key?: string;
}

const LiveRoomPage: React.FC = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const [room, setRoom] = useState<Room | null>(null);
  const [streamInfo, setStreamInfo] = useState<StreamInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [showStreamInfo, setShowStreamInfo] = useState(false);

  useEffect(() => {
    if (roomId) {
      loadRoom(parseInt(roomId));
    }
  }, [roomId]);

  const loadRoom = async (id: number) => {
    try {
      const [roomResponse, streamResponse] = await Promise.all([
        getRoom(id),
        getStreamInfo(id)
      ]);
      setRoom(roomResponse.data.room);
      setStreamInfo(streamResponse.data.stream_info);
    } catch (error) {
      console.error('Failed to load room:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <Typography>加载中...</Typography>
      </Container>
    );
  }

  if (!room) {
    return (
      <Container>
        <Typography>房间不存在</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 2 }}>
      <Grid container spacing={2}>
        {/* 左侧视频区域 */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 0, mb: 2 }}>
            <VideoPlayer roomId={room.id} isLive={room.is_live} />
          </Paper>
          
          {/* 房间信息 */}
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar
                src={room.user.avatar}
                sx={{ width: 48, height: 48, mr: 2 }}
              />
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h5" component="h1">
                  {room.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {room.user.nickname || room.user.username}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  label={room.category}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
                {room.is_live && (
                  <Chip
                    label="直播中"
                    size="small"
                    color="error"
                  />
                )}
                <Typography variant="body2" color="text.secondary">
                  观看: {room.view_count}
                </Typography>
              </Box>
            </Box>
            
            {room.description && (
              <Typography variant="body1" color="text.secondary">
                {room.description}
              </Typography>
            )}

            {/* 直播信息 - 仅在有推流信息时显示 */}
            {streamInfo && (streamInfo.push_url || streamInfo.stream_key) && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="h6" sx={{ flexGrow: 1 }}>
                    直播配置信息
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => setShowStreamInfo(!showStreamInfo)}
                    startIcon={showStreamInfo ? <VisibilityOff /> : <Visibility />}
                  >
                    {showStreamInfo ? '隐藏' : '显示'}
                  </Button>
                </Box>

                <Collapse in={showStreamInfo}>
                  <Box sx={{ mt: 2 }}>
                    {streamInfo.stream_url && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>播放地址:</strong>
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              backgroundColor: 'rgba(0,0,0,0.1)',
                              p: 1,
                              borderRadius: 1,
                              flexGrow: 1,
                              wordBreak: 'break-all'
                            }}
                          >
                            {streamInfo.stream_url}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => navigator.clipboard.writeText(streamInfo.stream_url || '')}
                          >
                            <ContentCopy fontSize="small" />
                          </IconButton>
                        </Box>
                      </Alert>
                    )}

                    {streamInfo.push_url && (
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>推流地址 (OBS设置):</strong>
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              backgroundColor: 'rgba(0,0,0,0.1)',
                              p: 1,
                              borderRadius: 1,
                              flexGrow: 1,
                              wordBreak: 'break-all'
                            }}
                          >
                            {streamInfo.push_url}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => navigator.clipboard.writeText(streamInfo.push_url || '')}
                          >
                            <ContentCopy fontSize="small" />
                          </IconButton>
                        </Box>
                      </Alert>
                    )}

                    {streamInfo.stream_key && (
                      <Alert severity="error">
                        <Typography variant="body2" sx={{ mb: 1 }}>
                          <strong>推流密钥:</strong>
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              backgroundColor: 'rgba(0,0,0,0.1)',
                              p: 1,
                              borderRadius: 1,
                              flexGrow: 1,
                              wordBreak: 'break-all'
                            }}
                          >
                            {streamInfo.stream_key}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => navigator.clipboard.writeText(streamInfo.stream_key || '')}
                          >
                            <ContentCopy fontSize="small" />
                          </IconButton>
                        </Box>
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          请妥善保管推流密钥，不要泄露给他人
                        </Typography>
                      </Alert>
                    )}
                  </Box>
                </Collapse>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* 右侧聊天和礼物区域 */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
            {/* 聊天室 */}
            <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <ChatRoom
                roomId={room.id}
                onWebSocketReady={(ws) => setWebsocket(ws)}
              />
            </Box>

            {/* 礼物面板 */}
            <Box sx={{ borderTop: 1, borderColor: 'divider' }}>
              <GiftPanel roomId={room.id} websocket={websocket || undefined} />
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default LiveRoomPage;
