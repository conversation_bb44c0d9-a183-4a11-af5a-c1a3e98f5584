import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar,
  Button,
  AppBar,
  Toolbar,
  Paper,
  Stack,

} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import {
  Person,
  Visibility,
  PlayArrow,
  AccountCircle,
  Logout,
  MonetizationOn,
  LiveTv,
  TrendingUp,
  SportsEsports,
  MusicNote,
  School,
  Palette,
  Restaurant,
  FitnessCenter,
  Pets,
  Explore,
  Whatshot,
  NewReleases,
  Star,
  ArrowForward,
} from '@mui/icons-material';
import { getRooms } from '../services/api';

interface Room {
  id: number;
  title: string;
  description: string;
  cover: string;
  is_live: boolean;
  view_count: number;
  category: string;
  user: {
    id: number;
    username: string;
    nickname: string;
    avatar: string;
  };
}

interface Category {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  count: number;
}

const categories: Category[] = [
  { id: 'gaming', name: '游戏', icon: <SportsEsports />, color: '#6366f1', count: 1250 },
  { id: 'music', name: '音乐', icon: <MusicNote />, color: '#ec4899', count: 680 },
  { id: 'education', name: '教育', icon: <School />, color: '#10b981', count: 420 },
  { id: 'art', name: '艺术', icon: <Palette />, color: '#f59e0b', count: 350 },
  { id: 'food', name: '美食', icon: <Restaurant />, color: '#ef4444', count: 280 },
  { id: 'fitness', name: '健身', icon: <FitnessCenter />, color: '#8b5cf6', count: 190 },
  { id: 'pets', name: '萌宠', icon: <Pets />, color: '#06b6d4', count: 150 },
  { id: 'other', name: '其他', icon: <LiveTv />, color: '#64748b', count: 320 },
];

const HomePage: React.FC = () => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadRooms();
    checkUserLogin();
  }, []);

  const checkUserLogin = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    if (token && userData) {
      setUser(JSON.parse(userData));
    }
  };

  const loadRooms = async () => {
    try {
      const response = await getRooms();
      setRooms(response.data.rooms || []);
    } catch (error) {
      console.error('Failed to load rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRoomClick = (roomId: number) => {
    navigate(`/room/${roomId}`);
  };

  const handleLogin = () => {
    navigate('/login');
  };

  const handleRegister = () => {
    navigate('/register');
  };

  const handleProfile = () => {
    navigate('/profile');
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setUser(null);
  };

  const handleCategoryClick = (categoryId: string) => {
    navigate(`/channel/${categoryId}`);
  };

  return (
    <Box>
      {/* 顶部导航栏 */}
      <AppBar position="static" elevation={0}>
        <Toolbar sx={{ py: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            <LiveTv sx={{ mr: 2, fontSize: 32 }} />
            <Typography
              variant="h5"
              component="div"
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(45deg, #ffffff, #f1f5f9)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              LiveWin
            </Typography>
            <Typography
              variant="body2"
              sx={{
                ml: 1,
                opacity: 0.9,
                fontWeight: 500,
              }}
            >
              直播平台
            </Typography>
          </Box>

          {user ? (
            <Stack direction="row" spacing={2} alignItems="center">
              <Paper
                sx={{
                  px: 2,
                  py: 0.5,
                  backgroundColor: 'rgba(255,255,255,0.15)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Stack direction="row" spacing={1} alignItems="center">
                  <MonetizationOn sx={{ fontSize: 18, color: '#fbbf24' }} />
                  <Typography variant="body2" color="inherit" fontWeight={600}>
                    {user.coins}
                  </Typography>
                </Stack>
              </Paper>

              <Stack direction="row" spacing={1} alignItems="center">
                <Avatar
                  src={user.avatar}
                  sx={{
                    width: 36,
                    height: 36,
                    border: '2px solid rgba(255,255,255,0.3)',
                  }}
                >
                  {user.nickname?.charAt(0) || user.username?.charAt(0)}
                </Avatar>
                <Typography variant="body1" color="inherit" fontWeight={600}>
                  {user.nickname || user.username}
                </Typography>
              </Stack>

              <Button
                color="inherit"
                onClick={handleProfile}
                startIcon={<AccountCircle />}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.2)' }
                }}
              >
                个人中心
              </Button>
              <Button
                color="inherit"
                onClick={handleLogout}
                startIcon={<Logout />}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  '&:hover': { backgroundColor: 'rgba(255,255,255,0.2)' }
                }}
              >
                退出
              </Button>
            </Stack>
          ) : (
            <Stack direction="row" spacing={2}>
              <Button
                color="inherit"
                onClick={handleLogin}
                variant="outlined"
                sx={{
                  borderColor: 'rgba(255,255,255,0.3)',
                  '&:hover': {
                    borderColor: 'rgba(255,255,255,0.5)',
                    backgroundColor: 'rgba(255,255,255,0.1)'
                  }
                }}
              >
                登录
              </Button>
              <Button
                onClick={handleRegister}
                variant="contained"
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  color: '#6366f1',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,1)',
                  }
                }}
              >
                注册
              </Button>
            </Stack>
          )}
        </Toolbar>
      </AppBar>

      {/* 主要内容区域 */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
          minHeight: 'calc(100vh - 80px)',
          py: 6,
        }}
      >
        <Container maxWidth="lg">
          {/* 页面标题和统计 */}
          <Box sx={{ mb: 6, textAlign: 'center' }}>
            <Typography
              variant="h3"
              component="h1"
              sx={{
                mb: 2,
                fontWeight: 800,
                background: 'linear-gradient(45deg, #6366f1, #ec4899)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              LiveWin 直播平台
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
              发现精彩内容，与主播实时互动
            </Typography>

            {/* 快速导航按钮 */}
            <Stack direction="row" spacing={2} justifyContent="center" sx={{ mb: 4 }}>
              <Button
                variant="contained"
                startIcon={<Whatshot />}
                size="large"
                sx={{
                  background: 'linear-gradient(135deg, #ef4444 0%, #f87171 100%)',
                  px: 3,
                }}
                onClick={() => navigate('/channel/all')}
              >
                热门直播
              </Button>
              <Button
                variant="outlined"
                startIcon={<NewReleases />}
                size="large"
                sx={{ px: 3 }}
                onClick={() => navigate('/channel/all')}
              >
                最新开播
              </Button>
              <Button
                variant="outlined"
                startIcon={<Star />}
                size="large"
                sx={{ px: 3 }}
              >
                精选推荐
              </Button>
              <Button
                variant="contained"
                startIcon={<LiveTv />}
                size="large"
                sx={{
                  background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                  px: 3,
                }}
                onClick={() => navigate('/manage')}
              >
                直播间管理
              </Button>
            </Stack>

            {/* 统计卡片 */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
              <Grid item xs={12} sm={4}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
                    color: 'white',
                  }}
                >
                  <LiveTv sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" fontWeight={700}>
                    {rooms.length}
                  </Typography>
                  <Typography variant="body1">
                    正在直播
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: 'linear-gradient(135deg, #ec4899 0%, #f472b6 100%)',
                    color: 'white',
                  }}
                >
                  <TrendingUp sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" fontWeight={700}>
                    {rooms.reduce((sum, room) => sum + room.view_count, 0)}
                  </Typography>
                  <Typography variant="body1">
                    总观看数
                  </Typography>
                </Paper>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    background: 'linear-gradient(135deg, #10b981 0%, #34d399 100%)',
                    color: 'white',
                  }}
                >
                  <Person sx={{ fontSize: 40, mb: 1 }} />
                  <Typography variant="h4" fontWeight={700}>
                    1.2K
                  </Typography>
                  <Typography variant="body1">
                    在线用户
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Box>

          {/* 分类导航 */}
          <Box sx={{ mb: 6 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" fontWeight={700}>
                浏览分类
              </Typography>
              <Button
                endIcon={<ArrowForward />}
                onClick={() => navigate('/channel/all')}
                sx={{ fontWeight: 600 }}
              >
                查看全部
              </Button>
            </Box>

            <Grid container spacing={3}>
              {categories.map((category) => (
                <Grid item xs={6} sm={4} md={3} key={category.id}>
                  <Paper
                    sx={{
                      p: 3,
                      textAlign: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      background: `linear-gradient(135deg, ${category.color}15 0%, ${category.color}25 100%)`,
                      border: `2px solid ${category.color}30`,
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: `0 8px 25px ${category.color}40`,
                        background: `linear-gradient(135deg, ${category.color}25 0%, ${category.color}35 100%)`,
                      },
                    }}
                    onClick={() => handleCategoryClick(category.id)}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 60,
                        height: 60,
                        borderRadius: 3,
                        backgroundColor: category.color,
                        color: 'white',
                        mx: 'auto',
                        mb: 2,
                      }}
                    >
                      {category.icon}
                    </Box>
                    <Typography variant="h6" fontWeight={600} gutterBottom>
                      {category.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {category.count} 个直播间
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </Box>

          {/* 热门推荐 */}
          <Box sx={{ mb: 6 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h4" fontWeight={700}>
                热门推荐
              </Typography>
              <Button
                endIcon={<ArrowForward />}
                onClick={() => navigate('/channel/all')}
                sx={{ fontWeight: 600 }}
              >
                查看更多
              </Button>
            </Box>
          </Box>

          {/* 直播间列表 */}
          {loading ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <Typography variant="h6" color="text.secondary">
                加载中...
              </Typography>
            </Box>
          ) : (
            <Grid container spacing={4}>
              {rooms.map((room) => (
                <Grid item xs={12} sm={6} lg={4} key={room.id}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      overflow: 'hidden',
                    }}
                    onClick={() => handleRoomClick(room.id)}
                  >
                    {/* 直播封面 */}
                    <Box sx={{ position: 'relative' }}>
                      <CardMedia
                        component="img"
                        height="200"
                        image={room.cover || 'https://via.placeholder.com/400x200/6366f1/ffffff?text=LiveWin'}
                        alt={room.title}
                        sx={{
                          backgroundColor: '#6366f1',
                          objectFit: 'cover',
                        }}
                      />

                      {/* 直播状态标签 */}
                      {room.is_live && (
                        <Chip
                          icon={<PlayArrow />}
                          label="直播中"
                          size="small"
                          sx={{
                            position: 'absolute',
                            top: 12,
                            left: 12,
                            backgroundColor: '#ef4444',
                            color: 'white',
                            fontWeight: 600,
                            '& .MuiChip-icon': { color: 'white' },
                          }}
                        />
                      )}

                      {/* 观看数 */}
                      <Paper
                        sx={{
                          position: 'absolute',
                          bottom: 12,
                          right: 12,
                          px: 1.5,
                          py: 0.5,
                          backgroundColor: 'rgba(0,0,0,0.7)',
                          color: 'white',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                        }}
                      >
                        <Visibility sx={{ fontSize: 16 }} />
                        <Typography variant="body2" fontWeight={600}>
                          {room.view_count}
                        </Typography>
                      </Paper>
                    </Box>

                    {/* 卡片内容 */}
                    <CardContent sx={{ flexGrow: 1, p: 3 }}>
                      <Typography
                        variant="h6"
                        component="h2"
                        sx={{
                          mb: 1,
                          fontWeight: 600,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                      >
                        {room.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          mb: 2,
                          height: '40px',
                          overflow: 'hidden',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}
                      >
                        {room.description || '暂无描述'}
                      </Typography>

                      {/* 主播信息 */}
                      <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
                        <Avatar
                          src={room.user.avatar}
                          sx={{ width: 32, height: 32 }}
                        >
                          <Person />
                        </Avatar>
                        <Typography variant="body2" fontWeight={500}>
                          {room.user.nickname || room.user.username}
                        </Typography>
                      </Stack>

                      {/* 分类标签 */}
                      <Chip
                        label={room.category}
                        size="small"
                        variant="outlined"
                        sx={{
                          borderRadius: 2,
                          fontWeight: 500,
                        }}
                      />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}

          {/* 空状态 */}
          {!loading && rooms.length === 0 && (
            <Paper
              sx={{
                textAlign: 'center',
                py: 8,
                px: 4,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
              }}
            >
              <LiveTv sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h5" color="text.secondary" sx={{ mb: 1 }}>
                暂无直播间
              </Typography>
              <Typography variant="body1" color="text.secondary">
                成为第一个开播的主播吧！
              </Typography>
              {user && (
                <Button
                  variant="contained"
                  size="large"
                  sx={{ mt: 3 }}
                  onClick={() => navigate('/profile')}
                >
                  开始直播
                </Button>
              )}
            </Paper>
          )}
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
