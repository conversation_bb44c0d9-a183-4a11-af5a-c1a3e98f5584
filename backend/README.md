# LiveWin 后端服务

基于 Go 语言开发的直播平台后端 API 服务。

## 功能特性

- 🔐 JWT 用户认证与授权
- 👤 用户管理系统
- 📺 直播间管理
- 🎁 礼物系统
- 📊 数据统计
- 🗄️ PostgreSQL 数据存储
- ⚡ Redis 缓存

## 技术栈

- **框架**: Gin Web Framework
- **数据库**: PostgreSQL + GORM
- **缓存**: Redis
- **认证**: JWT
- **密码加密**: bcrypt

## API 文档

### 认证相关

#### 用户注册
```
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 用户登录
```
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

### 用户相关

#### 获取用户信息
```
GET /api/v1/user/profile
Authorization: Bearer <token>
```

#### 更新用户信息
```
PUT /api/v1/user/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "nickname": "新昵称",
  "avatar": "头像URL"
}
```

### 房间相关

#### 获取直播间列表
```
GET /api/v1/room/list?page=1&limit=20
```

#### 获取房间详情
```
GET /api/v1/room/:id
```

#### 创建直播间
```
POST /api/v1/room/create
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "直播间标题",
  "description": "直播间描述"
}
```

#### 开始直播
```
POST /api/v1/room/:id/start
Authorization: Bearer <token>
```

#### 停止直播
```
POST /api/v1/room/:id/stop
Authorization: Bearer <token>
```

### 礼物相关

#### 获取礼物列表
```
GET /api/v1/gift/list
```

#### 发送礼物
```
POST /api/v1/gift/send
Authorization: Bearer <token>
Content-Type: application/json

{
  "room_id": 1,
  "gift_id": 1,
  "count": 1,
  "message": "礼物留言"
}
```

## 数据库模型

### User (用户)
- ID: 主键
- Username: 用户名
- Email: 邮箱
- Password: 密码哈希
- Nickname: 昵称
- Avatar: 头像
- Level: 等级
- Coins: 金币
- IsStreamer: 是否为主播

### Room (直播间)
- ID: 主键
- UserID: 用户ID
- Title: 标题
- Description: 描述
- StreamKey: 推流密钥
- IsLive: 是否直播中
- ViewCount: 观看次数

### Gift (礼物)
- ID: 主键
- Name: 礼物名称
- Icon: 礼物图标
- Price: 价格
- Effect: 特效

### GiftRecord (礼物记录)
- ID: 主键
- UserID: 发送用户ID
- RoomID: 房间ID
- GiftID: 礼物ID
- Count: 数量
- TotalCost: 总价

## 环境变量

```bash
PORT=8080
DATABASE_URL=postgres://user:password@localhost/livewin?sslmode=disable
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret-key
ENVIRONMENT=development
```

## 开发指南

### 本地开发

1. 安装依赖
```bash
go mod tidy
```

2. 启动服务
```bash
go run cmd/main.go
```

### 构建部署

```bash
go build -o main cmd/main.go
./main
```

### Docker 部署

```bash
docker build -t livewin-backend .
docker run -p 8080:8080 livewin-backend
```
