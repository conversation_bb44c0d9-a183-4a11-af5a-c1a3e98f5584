package main

import (
	"livewin/backend/internal/config"
	"livewin/backend/internal/database"
	"livewin/backend/internal/logger"
	"livewin/backend/internal/router"
	"livewin/backend/internal/service"
	"log"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 初始化日志系统
	logConfig := logger.LogConfig{
		Level:      cfg.LogLevel,
		LogDir:     cfg.LogDir,
		MaxSize:    100,
		MaxBackups: 7,
		MaxAge:     30,
	}
	if err := logger.Init(logConfig); err != nil {
		log.Fatal("Failed to initialize logger:", err)
	}
	logger.Info("LiveWin 后端服务启动中...")

	// 初始化数据库
	logger.Info("正在连接数据库...")
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		logger.Fatal("Failed to initialize database:", err)
	}
	logger.Info("数据库连接成功")

	// 初始化Redis
	logger.Info("正在连接Redis...")
	redis, err := database.InitializeRedis(cfg.RedisURL)
	if err != nil {
		logger.Fatal("Failed to initialize Redis:", err)
	}
	logger.Info("Redis连接成功")

	// 初始化服务
	logger.Info("正在初始化服务...")
	services := service.NewServices(db, redis)
	logger.Info("服务初始化完成")

	// 设置路由
	logger.Info("正在设置路由...")
	r := router.SetupRouter(services)
	logger.Info("路由设置完成")

	// 启动服务器
	logger.Infof("服务器启动在端口 %s", cfg.Port)
	logger.Infof("环境: %s", cfg.Environment)
	logger.Info("🚀 LiveWin 后端服务已启动")
	if err := r.Run(":" + cfg.Port); err != nil {
		logger.Fatal("Failed to start server:", err)
	}
}
