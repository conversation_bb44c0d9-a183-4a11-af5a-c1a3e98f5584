package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
)

var Logger *logrus.Logger

// LogConfig 日志配置
type LogConfig struct {
	Level      string // debug, info, warn, error
	LogDir     string // 日志文件目录
	MaxSize    int64  // 单个日志文件最大大小 (MB)
	MaxBackups int    // 保留的日志文件数量
	MaxAge     int    // 日志文件保留天数
}

// Init 初始化日志系统
func Init(config LogConfig) error {
	Logger = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	Logger.SetLevel(level)

	// 设置日志格式
	Logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
		PrettyPrint:     false,
	})

	// 创建日志目录
	if err := os.MkdirAll(config.LogDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 设置日志输出
	logFile, err := setupLogFile(config.LogDir)
	if err != nil {
		return fmt.Errorf("设置日志文件失败: %v", err)
	}

	// 同时输出到控制台和文件
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	Logger.SetOutput(multiWriter)

	Logger.Info("日志系统初始化完成")
	return nil
}

// setupLogFile 设置日志文件
func setupLogFile(logDir string) (*os.File, error) {
	// 生成日志文件名 (按日期)
	now := time.Now()
	filename := fmt.Sprintf("livewin-%s.log", now.Format("2006-01-02"))
	logPath := filepath.Join(logDir, filename)

	// 打开或创建日志文件
	logFile, err := os.OpenFile(logPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil, err
	}

	return logFile, nil
}

// GetLogger 获取日志实例
func GetLogger() *logrus.Logger {
	if Logger == nil {
		// 如果没有初始化，使用默认配置
		defaultConfig := LogConfig{
			Level:      "info",
			LogDir:     "./logs",
			MaxSize:    100,
			MaxBackups: 7,
			MaxAge:     30,
		}
		Init(defaultConfig)
	}
	return Logger
}

// 便捷方法
func Debug(args ...interface{}) {
	GetLogger().Debug(args...)
}

func Info(args ...interface{}) {
	GetLogger().Info(args...)
}

func Warn(args ...interface{}) {
	GetLogger().Warn(args...)
}

func Error(args ...interface{}) {
	GetLogger().Error(args...)
}

func Fatal(args ...interface{}) {
	GetLogger().Fatal(args...)
}

// 格式化日志方法
func Debugf(format string, args ...interface{}) {
	GetLogger().Debugf(format, args...)
}

func Infof(format string, args ...interface{}) {
	GetLogger().Infof(format, args...)
}

func Warnf(format string, args ...interface{}) {
	GetLogger().Warnf(format, args...)
}

func Errorf(format string, args ...interface{}) {
	GetLogger().Errorf(format, args...)
}

func Fatalf(format string, args ...interface{}) {
	GetLogger().Fatalf(format, args...)
}

// WithFields 带字段的日志
func WithFields(fields logrus.Fields) *logrus.Entry {
	return GetLogger().WithFields(fields)
}

// WithField 带单个字段的日志
func WithField(key string, value interface{}) *logrus.Entry {
	return GetLogger().WithField(key, value)
}

// LogRequest 记录HTTP请求日志
func LogRequest(method, path, clientIP string, statusCode int, latency time.Duration) {
	WithFields(logrus.Fields{
		"method":      method,
		"path":        path,
		"client_ip":   clientIP,
		"status_code": statusCode,
		"latency":     latency.String(),
	}).Info("HTTP Request")
}

// LogError 记录错误日志
func LogError(err error, context string) {
	WithFields(logrus.Fields{
		"error":   err.Error(),
		"context": context,
	}).Error("Error occurred")
}

// LogUserAction 记录用户操作日志
func LogUserAction(userID uint, action, details string) {
	WithFields(logrus.Fields{
		"user_id": userID,
		"action":  action,
		"details": details,
	}).Info("User Action")
}
