package router

import (
	"livewin/backend/internal/handler"
	"livewin/backend/internal/middleware"
	"livewin/backend/internal/service"

	"github.com/gin-gonic/gin"
)

func SetupRouter(services *service.Services) *gin.Engine {
	r := gin.Default()

	// 中间件
	r.Use(middleware.CORS())
	r.Use(middleware.Logger())

	// 创建处理器
	authHandler := handler.NewAuthHandler(services.Auth)
	userHandler := handler.NewUserHandler(services.User)
	roomHandler := handler.NewRoomHandler(services.Room)
	giftHandler := handler.NewGiftHandler(services.Gift)

	// API路由组
	api := r.Group("/api/v1")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
		}

		// 用户相关
		user := api.Group("/user")
		user.Use(middleware.AuthRequired(services.Auth))
		{
			user.GET("/profile", userHandler.GetProfile)
			user.PUT("/profile", userHandler.UpdateProfile)
			user.GET("/gifts", giftHandler.GetUserGiftHistory)
		}

		// 房间相关
		room := api.Group("/room")
		{
			room.GET("/list", roomHandler.GetLiveRooms)
			room.GET("/:id", roomHandler.GetRoom)
			room.GET("/:id/viewers", roomHandler.GetOnlineViewers)
			room.GET("/:id/stream", roomHandler.GetStreamInfo)
			room.GET("/:id/gifts", giftHandler.GetRoomGiftRecords)
		}

		// 需要认证的房间操作
		roomAuth := api.Group("/room")
		roomAuth.Use(middleware.AuthRequired(services.Auth))
		{
			roomAuth.POST("/create", roomHandler.CreateRoom)
			roomAuth.PUT("/:id", roomHandler.UpdateRoom)
			roomAuth.POST("/:id/start", roomHandler.StartLive)
			roomAuth.POST("/:id/stop", roomHandler.StopLive)
			roomAuth.POST("/:id/view", roomHandler.IncrementView)
		}

		// 礼物相关
		gift := api.Group("/gift")
		{
			gift.GET("/list", giftHandler.GetAllGifts)
		}

		// 需要认证的礼物操作
		giftAuth := api.Group("/gift")
		giftAuth.Use(middleware.AuthRequired(services.Auth))
		{
			giftAuth.POST("/send", giftHandler.SendGift)
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	return r
}
