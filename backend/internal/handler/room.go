package handler

import (
	"livewin/backend/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type RoomHandler struct {
	roomService *service.RoomService
}

type CreateRoomRequest struct {
	Title       string `json:"title" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
}

type UpdateRoomRequest struct {
	Title       string `json:"title" binding:"max=100"`
	Description string `json:"description" binding:"max=500"`
	Category    string `json:"category"`
	Tags        string `json:"tags"`
}

func NewRoomHandler(roomService *service.RoomService) *RoomHandler {
	return &RoomHandler{
		roomService: roomService,
	}
}

func (h *RoomHandler) CreateRoom(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	var req CreateRoomRequest
	if err := c.<PERSON>ind<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>rror()})
		return
	}

	room, err := h.roomService.CreateRoom(userID.(uint), req.Title, req.Description)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "创建成功",
		"room":    room,
	})
}

func (h *RoomHandler) GetRoom(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	room, err := h.roomService.GetRoom(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "房间不存在"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"room": room,
	})
}

func (h *RoomHandler) GetLiveRooms(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	rooms, err := h.roomService.GetLiveRooms(page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取房间列表失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"rooms": rooms,
		"page":  page,
		"limit": limit,
	})
}

func (h *RoomHandler) UpdateRoom(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	// 检查房间所有权
	room, err := h.roomService.GetRoom(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "房间不存在"})
		return
	}

	if room.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "无权限操作"})
		return
	}

	var req UpdateRoomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updates := make(map[string]interface{})
	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.Tags != "" {
		updates["tags"] = req.Tags
	}

	if err := h.roomService.UpdateRoom(uint(id), updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "更新成功",
	})
}

func (h *RoomHandler) StartLive(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	// 检查房间所有权
	room, err := h.roomService.GetRoom(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "房间不存在"})
		return
	}

	if room.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "无权限操作"})
		return
	}

	if err := h.roomService.StartLive(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "开始直播失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "直播已开始",
	})
}

func (h *RoomHandler) StopLive(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	// 检查房间所有权
	room, err := h.roomService.GetRoom(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "房间不存在"})
		return
	}

	if room.UserID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "无权限操作"})
		return
	}

	if err := h.roomService.StopLive(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "停止直播失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "直播已停止",
	})
}

func (h *RoomHandler) IncrementView(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	if err := h.roomService.IncrementViewCount(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "更新观看数失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "观看数已更新",
	})
}

func (h *RoomHandler) GetOnlineViewers(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	count, err := h.roomService.GetOnlineViewers(uint(id))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取在线人数失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"viewers": count,
	})
}
