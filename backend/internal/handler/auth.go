package handler

import (
	"livewin/backend/internal/logger"
	"livewin/backend/internal/service"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

type AuthHandler struct {
	authService *service.AuthService
}

type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=3"`
}

type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

func NewAuthHandler(authService *service.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		logger.WithField("error", err.Error()).Warn("用户注册请求参数错误")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": err.<PERSON>()})
		return
	}

	logger.WithField("username", req.Username).Info("用户注册请求")

	user, err := h.authService.Register(req.Username, req.Email, req.Password)
	if err != nil {
		logger.WithFields(logrus.Fields{
			"username": req.Username,
			"email":    req.Email,
			"error":    err.Error(),
		}).Error("用户注册失败")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 生成token
	token, err := h.authService.GenerateToken(user)
	if err != nil {
		logger.WithField("user_id", user.ID).Error("生成token失败")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "生成token失败"})
		return
	}

	logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("用户注册成功")

	c.JSON(http.StatusCreated, gin.H{
		"message": "注册成功",
		"user":    user,
		"token":   token,
	})
}

func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	user, token, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "登录成功",
		"user":    user,
		"token":   token,
	})
}
