package handler

import (
	"livewin/backend/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type GiftHandler struct {
	giftService *service.GiftService
}

type SendGiftRequest struct {
	RoomID  uint   `json:"room_id" binding:"required"`
	GiftID  uint   `json:"gift_id" binding:"required"`
	Count   int    `json:"count" binding:"required,min=1,max=999"`
	Message string `json:"message" binding:"max=200"`
}

func NewGiftHandler(giftService *service.GiftService) *GiftHandler {
	return &GiftHandler{
		giftService: giftService,
	}
}

func (h *GiftHandler) GetAllGifts(c *gin.Context) {
	gifts, err := h.giftService.GetAllGifts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取礼物列表失败"})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"gifts": gifts,
	})
}

func (h *GiftHandler) SendGift(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	var req SendGiftRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	giftRecord, err := h.giftService.SendGift(userID.(uint), req.RoomID, req.GiftID, req.Count, req.Message)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":     "礼物发送成功",
		"gift_record": giftRecord,
	})
}

func (h *GiftHandler) GetRoomGiftRecords(c *gin.Context) {
	idStr := c.Param("id")
	roomID, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "无效的房间ID"})
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if limit < 1 || limit > 100 {
		limit = 50
	}

	records, err := h.giftService.GetGiftRecords(uint(roomID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取礼物记录失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"records": records,
	})
}

func (h *GiftHandler) GetUserGiftHistory(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "未认证"})
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	records, err := h.giftService.GetUserGiftHistory(userID.(uint), page, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "获取礼物历史失败"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"records": records,
		"page":    page,
		"limit":   limit,
	})
}
