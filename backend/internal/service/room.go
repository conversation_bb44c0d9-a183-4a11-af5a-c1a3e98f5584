package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"livewin/backend/internal/models"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type RoomService struct {
	db    *gorm.DB
	redis *redis.Client
}

func NewRoomService(db *gorm.DB, redis *redis.Client) *RoomService {
	return &RoomService{
		db:    db,
		redis: redis,
	}
}

func (s *RoomService) CreateRoom(userID uint, title, description string) (*models.Room, error) {
	// 检查用户是否已有房间
	var existingRoom models.Room
	if err := s.db.Where("user_id = ?", userID).First(&existingRoom).Error; err == nil {
		return nil, fmt.Errorf("用户已有直播间")
	}

	// 生成推流密钥
	streamKey, err := generateStreamKey()
	if err != nil {
		return nil, err
	}

	room := &models.Room{
		UserID:      userID,
		Title:       title,
		Description: description,
		StreamKey:   streamKey,
		Category:    "其他",
	}

	if err := s.db.Create(room).Error; err != nil {
		return nil, err
	}

	// 生成直播地址
	s.generateStreamURLs(room)

	// 预加载用户信息
	s.db.Preload("User").First(room, room.ID)

	return room, nil
}

func (s *RoomService) GetRoom(id uint) (*models.Room, error) {
	var room models.Room
	if err := s.db.Preload("User").First(&room, id).Error; err != nil {
		return nil, err
	}
	return &room, nil
}

func (s *RoomService) GetLiveRooms(page, limit int) ([]models.Room, error) {
	var rooms []models.Room
	offset := (page - 1) * limit

	if err := s.db.Preload("User").Where("is_live = ?", true).
		Order("view_count DESC").
		Offset(offset).Limit(limit).
		Find(&rooms).Error; err != nil {
		return nil, err
	}

	return rooms, nil
}

func (s *RoomService) UpdateRoom(id uint, updates map[string]interface{}) error {
	return s.db.Model(&models.Room{}).Where("id = ?", id).Updates(updates).Error
}

func (s *RoomService) StartLive(roomID uint) error {
	if err := s.db.Model(&models.Room{}).Where("id = ?", roomID).Update("is_live", true).Error; err != nil {
		return err
	}

	// 在Redis中记录直播状态
	s.redis.Set(context.Background(), fmt.Sprintf("room:live:%d", roomID), "true", 0)

	return nil
}

func (s *RoomService) StopLive(roomID uint) error {
	if err := s.db.Model(&models.Room{}).Where("id = ?", roomID).Update("is_live", false).Error; err != nil {
		return err
	}

	// 从Redis中移除直播状态
	s.redis.Del(context.Background(), fmt.Sprintf("room:live:%d", roomID))

	return nil
}

func (s *RoomService) IncrementViewCount(roomID uint) error {
	// 更新数据库
	if err := s.db.Model(&models.Room{}).Where("id = ?", roomID).Update("view_count", gorm.Expr("view_count + 1")).Error; err != nil {
		return err
	}

	// 更新Redis中的实时观看数
	key := fmt.Sprintf("room:viewers:%d", roomID)
	s.redis.Incr(context.Background(), key)
	s.redis.Expire(context.Background(), key, 24*time.Hour)

	return nil
}

func (s *RoomService) GetOnlineViewers(roomID uint) (int64, error) {
	key := fmt.Sprintf("room:viewers:%d", roomID)
	count, err := s.redis.Get(context.Background(), key).Int64()
	if err == redis.Nil {
		return 0, nil
	}
	return count, err
}

func generateStreamKey() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateStreamURLs 生成直播相关的URL
func (s *RoomService) generateStreamURLs(room *models.Room) {
	// 这里可以根据实际的流媒体服务器配置来生成URL
	// 示例使用本地配置，实际部署时应该从配置文件读取

	baseURL := "rtmp://localhost:1935/live"   // RTMP服务器地址
	hlsBaseURL := "http://localhost:8080/hls" // HLS播放地址

	// 生成推流地址 (用于OBS等推流软件)
	room.PushURL = fmt.Sprintf("%s/%s", baseURL, room.StreamKey)

	// 生成播放地址 (用于前端播放器)
	room.StreamURL = fmt.Sprintf("%s/%s.m3u8", hlsBaseURL, room.StreamKey)

	// 更新数据库
	s.db.Model(room).Updates(map[string]interface{}{
		"push_url":   room.PushURL,
		"stream_url": room.StreamURL,
	})
}
