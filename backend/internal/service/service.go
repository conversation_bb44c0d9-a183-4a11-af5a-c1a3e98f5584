package service

import (
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type Services struct {
	User *UserService
	Room *RoomService
	Gift *GiftService
	Auth *AuthService
}

func NewServices(db *gorm.DB, redis *redis.Client) *Services {
	return &Services{
		User: NewUserService(db, redis),
		Room: NewRoomService(db, redis),
		Gift: NewGiftService(db, redis),
		Auth: NewAuthService(db, redis),
	}
}
