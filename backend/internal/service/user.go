package service

import (
	"context"
	"encoding/json"
	"fmt"
	"livewin/backend/internal/models"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type UserService struct {
	db    *gorm.DB
	redis *redis.Client
}

func NewUserService(db *gorm.DB, redis *redis.Client) *UserService {
	return &UserService{
		db:    db,
		redis: redis,
	}
}

func (s *UserService) GetUserByID(id uint) (*models.User, error) {
	// 先从Redis缓存中查找
	cacheKey := fmt.Sprintf("user:%d", id)
	cached, err := s.redis.Get(context.Background(), cacheKey).Result()
	if err == nil {
		var user models.User
		if err := json.Unmarshal([]byte(cached), &user); err == nil {
			return &user, nil
		}
	}

	// 从数据库查找
	var user models.User
	if err := s.db.First(&user, id).Error; err != nil {
		return nil, err
	}

	// 缓存到Redis
	userJSON, _ := json.Marshal(user)
	s.redis.Set(context.Background(), cacheKey, userJSON, 10*time.Minute)

	return &user, nil
}

func (s *UserService) UpdateUser(id uint, updates map[string]interface{}) error {
	if err := s.db.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("user:%d", id)
	s.redis.Del(context.Background(), cacheKey)

	return nil
}

func (s *UserService) GetUserProfile(id uint) (*models.User, error) {
	var user models.User
	if err := s.db.Select("id, username, nickname, avatar, level, coins, is_streamer, created_at").First(&user, id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (s *UserService) UpdateCoins(userID uint, amount int64) error {
	return s.db.Model(&models.User{}).Where("id = ?", userID).Update("coins", gorm.Expr("coins + ?", amount)).Error
}

func (s *UserService) DeductCoins(userID uint, amount int64) error {
	result := s.db.Model(&models.User{}).Where("id = ? AND coins >= ?", userID, amount).Update("coins", gorm.Expr("coins - ?", amount))
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("余额不足")
	}
	return nil
}
