package service

import (
	"context"
	"encoding/json"
	"fmt"
	"livewin/backend/internal/models"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type GiftService struct {
	db    *gorm.DB
	redis *redis.Client
}

func NewGiftService(db *gorm.DB, redis *redis.Client) *GiftService {
	return &GiftService{
		db:    db,
		redis: redis,
	}
}

func (s *GiftService) GetAllGifts() ([]models.Gift, error) {
	// 先从Redis缓存中获取
	cacheKey := "gifts:all"
	cached, err := s.redis.Get(context.Background(), cacheKey).Result()
	if err == nil {
		var gifts []models.Gift
		if err := json.Unmarshal([]byte(cached), &gifts); err == nil {
			return gifts, nil
		}
	}

	// 从数据库获取
	var gifts []models.Gift
	if err := s.db.Where("is_active = ?", true).Order("price ASC").Find(&gifts).Error; err != nil {
		return nil, err
	}

	// 缓存到Redis
	giftsJSON, _ := json.Marshal(gifts)
	s.redis.Set(context.Background(), cacheKey, giftsJSON, 30*time.Minute)

	return gifts, nil
}

func (s *GiftService) SendGift(userID, roomID, giftID uint, count int, message string) (*models.GiftRecord, error) {
	// 获取礼物信息
	var gift models.Gift
	if err := s.db.First(&gift, giftID).Error; err != nil {
		return nil, fmt.Errorf("礼物不存在")
	}

	// 计算总价
	totalCost := gift.Price * int64(count)

	// 开始事务
	tx := s.db.Begin()

	// 扣除用户金币
	result := tx.Model(&models.User{}).Where("id = ? AND coins >= ?", userID, totalCost).
		Update("coins", gorm.Expr("coins - ?", totalCost))
	if result.Error != nil {
		tx.Rollback()
		return nil, result.Error
	}
	if result.RowsAffected == 0 {
		tx.Rollback()
		return nil, fmt.Errorf("余额不足")
	}

	// 创建礼物记录
	giftRecord := &models.GiftRecord{
		UserID:    userID,
		RoomID:    roomID,
		GiftID:    giftID,
		Count:     count,
		TotalCost: totalCost,
		Message:   message,
	}

	if err := tx.Create(giftRecord).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 给主播增加收益（扣除平台分成后）
	var room models.Room
	if err := tx.First(&room, roomID).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 平台分成30%，主播得70%
	streamerIncome := int64(float64(totalCost) * 0.7)
	if err := tx.Model(&models.User{}).Where("id = ?", room.UserID).
		Update("coins", gorm.Expr("coins + ?", streamerIncome)).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	// 预加载关联数据
	s.db.Preload("User").Preload("Room").Preload("Gift").First(giftRecord, giftRecord.ID)

	// 在Redis中记录礼物统计
	s.recordGiftStats(roomID, giftID, count, totalCost)

	return giftRecord, nil
}

func (s *GiftService) GetGiftRecords(roomID uint, limit int) ([]models.GiftRecord, error) {
	var records []models.GiftRecord
	if err := s.db.Preload("User").Preload("Gift").
		Where("room_id = ?", roomID).
		Order("created_at DESC").
		Limit(limit).
		Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

func (s *GiftService) GetUserGiftHistory(userID uint, page, limit int) ([]models.GiftRecord, error) {
	var records []models.GiftRecord
	offset := (page - 1) * limit

	if err := s.db.Preload("Room").Preload("Gift").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).Limit(limit).
		Find(&records).Error; err != nil {
		return nil, err
	}
	return records, nil
}

func (s *GiftService) recordGiftStats(roomID, giftID uint, count int, totalCost int64) {
	ctx := context.Background()
	today := time.Now().Format("2006-01-02")

	// 房间今日礼物统计
	roomStatsKey := fmt.Sprintf("gift:stats:room:%d:%s", roomID, today)
	s.redis.HIncrBy(ctx, roomStatsKey, "total_gifts", int64(count))
	s.redis.HIncrBy(ctx, roomStatsKey, "total_value", totalCost)
	s.redis.Expire(ctx, roomStatsKey, 7*24*time.Hour)

	// 礼物类型统计
	giftStatsKey := fmt.Sprintf("gift:stats:gift:%d:%s", giftID, today)
	s.redis.HIncrBy(ctx, giftStatsKey, "count", int64(count))
	s.redis.Expire(ctx, giftStatsKey, 7*24*time.Hour)
}
