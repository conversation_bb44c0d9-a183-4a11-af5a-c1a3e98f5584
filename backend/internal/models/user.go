package models

import (
	"time"

	"gorm.io/gorm"
)

type User struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	Username   string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Email      string         `json:"email" gorm:"uniqueIndex;size:100;not null"`
	Password   string         `json:"-" gorm:"not null"`
	Nickname   string         `json:"nickname"`
	Avatar     string         `json:"avatar"`
	Level      int            `json:"level" gorm:"default:1"`
	Coins      int64          `json:"coins" gorm:"default:0"`
	IsStreamer bool           `json:"is_streamer" gorm:"default:false"`
	Status     string         `json:"status" gorm:"default:active"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`
}

type Room struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id" gorm:"not null"`
	User        *User          `json:"user"`
	Title       string         `json:"title" gorm:"not null"`
	Description string         `json:"description"`
	Cover       string         `json:"cover"`
	StreamKey   string         `json:"stream_key" gorm:"uniqueIndex;size:150;not null"`
	StreamURL   string         `json:"stream_url" gorm:"size:255"` // 直播播放地址
	PushURL     string         `json:"push_url" gorm:"size:255"`   // 推流地址
	IsLive      bool           `json:"is_live" gorm:"default:false"`
	ViewCount   int64          `json:"view_count" gorm:"default:0"`
	Category    string         `json:"category"`
	Tags        string         `json:"tags"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

type Gift struct {
	ID       uint   `json:"id" gorm:"primaryKey"`
	Name     string `json:"name" gorm:"not null"`
	Icon     string `json:"icon" gorm:"not null"`
	Price    int64  `json:"price" gorm:"not null"`
	Effect   string `json:"effect"`
	Category string `json:"category"`
	IsActive bool   `json:"is_active" gorm:"default:true"`
}

type GiftRecord struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"not null"`
	User      User      `json:"user"`
	RoomID    uint      `json:"room_id" gorm:"not null"`
	Room      Room      `json:"room"`
	GiftID    uint      `json:"gift_id" gorm:"not null"`
	Gift      Gift      `json:"gift"`
	Count     int       `json:"count" gorm:"default:1"`
	TotalCost int64     `json:"total_cost" gorm:"not null"`
	Message   string    `json:"message"`
	CreatedAt time.Time `json:"created_at"`
}
