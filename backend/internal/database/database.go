package database

import (
	"context"
	"livewin/backend/internal/models"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func Initialize(databaseURL string) (*gorm.DB, error) {
	// Auto-detect database type and open connection
	var db *gorm.DB
	var err error

	// For now, we only support MySQL since that's what's in go.mod
	// In the future, you can add PostgreSQL support by:
	// 1. Adding "gorm.io/driver/postgres" to go.mod
	// 2. Uncommenting the postgres detection logic below

	db, err = gorm.Open(mysql.Open(databaseURL), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 自动迁移数据库表
	err = db.AutoMigrate(
		&models.User{},
		&models.Room{},
		&models.Gift{},
		&models.GiftRecord{},
	)
	if err != nil {
		return nil, err
	}

	// 初始化默认礼物数据
	initDefaultGifts(db)

	return db, nil
}

func InitializeRedis(redisURL string) (*redis.Client, error) {
	opt, err := redis.ParseURL(redisURL)
	if err != nil {
		return nil, err
	}

	rdb := redis.NewClient(opt)

	// 测试连接
	_, err = rdb.Ping(context.Background()).Result()
	if err != nil {
		return nil, err
	}

	return rdb, nil
}

func initDefaultGifts(db *gorm.DB) {
	gifts := []models.Gift{
		{Name: "小心心", Icon: "❤️", Price: 1, Effect: "heart", Category: "basic"},
		{Name: "玫瑰花", Icon: "🌹", Price: 5, Effect: "rose", Category: "basic"},
		{Name: "啤酒", Icon: "🍺", Price: 10, Effect: "beer", Category: "drink"},
		{Name: "蛋糕", Icon: "🎂", Price: 20, Effect: "cake", Category: "food"},
		{Name: "跑车", Icon: "🏎️", Price: 100, Effect: "car", Category: "luxury"},
		{Name: "火箭", Icon: "🚀", Price: 500, Effect: "rocket", Category: "luxury"},
	}

	for _, gift := range gifts {
		var existingGift models.Gift
		if err := db.Where("name = ?", gift.Name).First(&existingGift).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				db.Create(&gift)
			}
		}
	}
}
