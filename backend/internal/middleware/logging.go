package middleware

import (
	"livewin/backend/internal/logger"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggingMiddleware 日志中间件
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 记录到我们的日志系统
		logger.WithFields(logrus.Fields{
			"method":      param.Method,
			"path":        param.Path,
			"status_code": param.StatusCode,
			"latency":     param.Latency,
			"client_ip":   param.ClientIP,
			"user_agent":  param.Request.UserAgent(),
			"error":       param.ErrorMessage,
		}).Info("HTTP Request")

		// 返回空字符串，因为我们已经记录到了我们的日志系统
		return ""
	})
}

// RequestLogger 请求日志记录器
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)

		// 获取状态码
		statusCode := c.Writer.Status()

		// 构建完整路径
		if raw != "" {
			path = path + "?" + raw
		}

		// 记录日志
		fields := logrus.Fields{
			"method":      c.Request.Method,
			"path":        path,
			"status_code": statusCode,
			"latency":     latency.String(),
			"client_ip":   c.ClientIP(),
			"user_agent":  c.Request.UserAgent(),
		}

		// 根据状态码选择日志级别
		switch {
		case statusCode >= 500:
			logger.WithFields(fields).Error("Server Error")
		case statusCode >= 400:
			logger.WithFields(fields).Warn("Client Error")
		case statusCode >= 300:
			logger.WithFields(fields).Info("Redirect")
		default:
			logger.WithFields(fields).Info("Success")
		}
	}
}

// ErrorLogger 错误日志中间件
func ErrorLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.WithFields(logrus.Fields{
					"method":     c.Request.Method,
					"path":       c.Request.URL.Path,
					"client_ip":  c.ClientIP(),
					"error_type": err.Type,
					"error":      err.Error(),
				}).Error("Request Error")
			}
		}
	}
}
