# Go 后端 .gitignore

# 二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件，使用 go test -c 构建
*.test

# 输出的二进制文件（go build）
main
backend
app
server

# Go 工作空间文件
go.work

# 依赖管理
vendor/

# 环境变量
.env
.env.local
.env.development
.env.production

# 日志文件
*.log
logs/

# 临时文件
tmp/
temp/
*.tmp

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（如果包含敏感信息）
config/local.yaml
config/production.yaml

# 上传文件目录
uploads/
static/uploads/

# 缓存目录
cache/

# 性能分析文件
*.prof
*.pprof

# 覆盖率报告
coverage.out
coverage.html

# IDE 文件
.vscode/
.idea/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# Air 热重载工具
tmp/
.air.toml

# 文档生成
docs/

# 备份文件
*.bak
*.backup
